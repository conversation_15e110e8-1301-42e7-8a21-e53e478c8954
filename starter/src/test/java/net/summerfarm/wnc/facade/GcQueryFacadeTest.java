package net.summerfarm.wnc.facade;

import net.summerfarm.wnc.facade.gc.GcQueryFacade;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDetailDTO;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Description: <br/>
 * date: 2025/6/26 15:42<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class GcQueryFacadeTest {

    @Resource
    private GcQueryFacade gcQueryFacade;

    @Test
    public void querySkuListIsUseInfoTest(){
        List<String> skus = Arrays.asList("50350652534", "2198158406115", "979630745633");
        /*List<ProductSkuDetailDTO> productSkuDetailDTOS = gcQueryFacade.querySkuListIsUseInfo(skus);
        System.out.println(productSkuDetailDTOS);*/
        List<ProductSkuDetailDTO> productSkuDetailDTOS1 = gcQueryFacade.querySaasProxyListBySkus(skus);
        System.out.println(productSkuDetailDTOS1);

    }
}
