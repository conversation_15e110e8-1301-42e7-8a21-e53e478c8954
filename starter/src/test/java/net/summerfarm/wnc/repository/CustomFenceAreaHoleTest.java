package net.summerfarm.wnc.repository;

import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEsEntity;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsCommandRepository;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 自定义围栏区域孔洞测试
 * 专门测试带孔洞的多边形功能
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CustomFenceAreaHoleTest {

    @Resource
    private CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;
    @Resource
    private CustomFenceAreaEsCommandRepository customFenceAreaEsCommandRepository;

    /**
     * 测试单个孔洞的多边形
     */
    @Test
    public void testSingleHolePolygon() {
        // 外环：大矩形 (120.0,30.0) 到 (120.4,30.4)
        // 内环：小矩形孔洞 (120.15,30.15) 到 (120.25,30.25)
        String geoJsonPolygon =
            "[[120.0,30.0],[120.4,30.0],[120.4,30.4],[120.0,30.4],[120.0,30.0]]";// 外环（顺时针）

        CustomFenceAreaEsEntity entity = new CustomFenceAreaEsEntity();
        entity.setId("single-hole-polygon2");
        entity.setAdCodeMsgId(1001);
        entity.setProvince("浙江省");
        entity.setCity("杭州市");
        entity.setArea("上城区1234");
        entity.setGeoShape(geoJsonPolygon);
        entity.setStatus(0);
        
        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("✓ 单孔洞多边形保存成功");
            
            // 测试查询
            testHolePolygonQuery(1001);
            
        } catch (Exception e) {
            System.err.println("✗ 单孔洞多边形保存失败: " + e.getMessage());
            e.printStackTrace();
        }

        testMultipleHolesPolygon();
    }

    /**
     * 测试多个孔洞的多边形
     */
    @Test
    public void testMultipleHolesPolygon() {
        // 外环：大矩形
        // 第一个孔洞：左下角小矩形
        // 第二个孔洞：右上角小矩形
        String geoJsonPolygon = "[" +
            "[[120.0,30.0],[120.6,30.0],[120.6,30.6],[120.0,30.6],[120.0,30.0]]," +  // 外环
            "[[120.05,30.05],[120.05,30.15],[120.15,30.15],[120.15,30.05],[120.05,30.05]]," +  // 孔洞1
            "[[120.45,30.45],[120.45,30.55],[120.55,30.55],[120.55,30.45],[120.45,30.45]]" +   // 孔洞2
            "]";
        
        CustomFenceAreaEsEntity entity = new CustomFenceAreaEsEntity();
        entity.setId("multiple-holes-polygon");
        entity.setAdCodeMsgId(1002);
        entity.setProvince("浙江省");
        entity.setCity("杭州市");
        entity.setArea("上城区");
        entity.setGeoShape(geoJsonPolygon);
        entity.setStatus(0);
        
        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("✓ 多孔洞多边形保存成功");
            
            // 测试查询
            testMultipleHolesQuery(1002);
            
        } catch (Exception e) {
            System.err.println("✗ 多孔洞多边形保存失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试复杂形状的孔洞多边形
     */
    @Test
    public void testComplexShapeWithHole() {
        // 外环：不规则多边形
        // 内环：三角形孔洞
        String geoJsonPolygon = "[" +
            "[[120.0,30.0],[120.3,30.1],[120.5,30.3],[120.4,30.5],[120.1,30.4],[120.0,30.2],[120.0,30.0]]," +  // 外环（不规则形状）
            "[[120.2,30.2],[120.3,30.3],[120.1,30.3],[120.2,30.2]]" +  // 内环（三角形孔洞）
            "]";

        CustomFenceAreaEsEntity entity = new CustomFenceAreaEsEntity();
        entity.setId("complex-shape-hole");
        entity.setAdCodeMsgId(1003);
        entity.setProvince("浙江省");
        entity.setCity("杭州市");
        entity.setArea("上城区123");
        entity.setGeoShape(geoJsonPolygon);
        entity.setStatus(0);

        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("✓ 复杂形状孔洞多边形保存成功");
        } catch (Exception e) {
            System.err.println("✗ 复杂形状孔洞多边形保存失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试孔洞多边形的空间查询
     */
    private void testHolePolygonQuery(Integer adCodeMsgId) {
        try {
            // 等待ES索引刷新
            Thread.sleep(2000);

            // 测试点在外环内但不在孔洞内的查询（应该匹配）
            String pointInPolygon = "120.1,30.1"; // 在外环内，不在孔洞内
            Integer result1 = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(
                Arrays.asList(adCodeMsgId), pointInPolygon);

            if (result1 != null && result1.equals(adCodeMsgId)) {
                System.out.println("✓ 外环内非孔洞区域查询成功");
            } else {
                System.out.println("✗ 外环内非孔洞区域查询失败");
            }

            // 测试点在孔洞内的查询（应该不匹配）
            String pointInHole = "120.2,30.2"; // 在孔洞内
            Integer result2 = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(
                Arrays.asList(adCodeMsgId), pointInHole);

            if (result2 == null) {
                System.out.println("✓ 孔洞内区域查询正确返回null");
            } else {
                System.out.println("✗ 孔洞内区域查询应该返回null，但返回了: " + result2);
            }

            // 测试点在外环外的查询（应该不匹配）
            String pointOutside = "120.5,30.5"; // 在外环外
            Integer result3 = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(
                Arrays.asList(adCodeMsgId), pointOutside);

            if (result3 == null) {
                System.out.println("✓ 外环外区域查询正确返回null");
            } else {
                System.out.println("✗ 外环外区域查询应该返回null，但返回了: " + result3);
            }

        } catch (Exception e) {
            System.err.println("✗ 孔洞多边形查询测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试多孔洞多边形的空间查询
     */
    private void testMultipleHolesQuery(Integer adCodeMsgId) {
        try {
            // 等待ES索引刷新
            Thread.sleep(2000);

            // 测试点在外环内但不在任何孔洞内的查询（应该匹配）
            String pointInPolygon = "120.3,30.3"; // 在外环内，不在任何孔洞内
            Integer result1 = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(
                Arrays.asList(adCodeMsgId), pointInPolygon);

            if (result1 != null && result1.equals(adCodeMsgId)) {
                System.out.println("✓ 多孔洞多边形有效区域查询成功");
            } else {
                System.out.println("✗ 多孔洞多边形有效区域查询失败");
            }

            // 测试点在第一个孔洞内的查询（应该不匹配）
            String pointInHole1 = "120.1,30.1"; // 在第一个孔洞内
            Integer result2 = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(
                Arrays.asList(adCodeMsgId), pointInHole1);

            if (result2 == null) {
                System.out.println("✓ 第一个孔洞内查询正确返回null");
            } else {
                System.out.println("✗ 第一个孔洞内查询应该返回null");
            }

            // 测试点在第二个孔洞内的查询（应该不匹配）
            String pointInHole2 = "120.5,30.5"; // 在第二个孔洞内
            Integer result3 = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(
                Arrays.asList(adCodeMsgId), pointInHole2);

            if (result3 == null) {
                System.out.println("✓ 第二个孔洞内查询正确返回null");
            } else {
                System.out.println("✗ 第二个孔洞内查询应该返回null");
            }

        } catch (Exception e) {
            System.err.println("✗ 多孔洞多边形查询测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 工具方法：创建带孔洞的GeoJSON多边形
     * @param outerRing 外环坐标字符串
     * @param holes 孔洞坐标字符串列表
     * @return GeoJSON格式字符串
     */
    public static String createPolygonWithHoles(String outerRing, List<String> holes) {
        StringBuilder geoJson = new StringBuilder();
        geoJson.append("{\"type\":\"polygon\",\"coordinates\":[");

        // 添加外环
        geoJson.append(convertCoordinateString(outerRing));

        // 添加孔洞
        for (String hole : holes) {
            geoJson.append(",").append(convertCoordinateString(hole));
        }

        geoJson.append("]}");
        return geoJson.toString();
    }

    /**
     * 将坐标字符串转换为坐标数组格式
     */
    private static String convertCoordinateString(String coordinatesStr) {
        String[] points = coordinatesStr.split(";");
        StringBuilder coords = new StringBuilder();
        coords.append("[");

        for (int i = 0; i < points.length; i++) {
            String[] lonLat = points[i].split(",");
            if (lonLat.length == 2) {
                coords.append("[").append(lonLat[0]).append(",").append(lonLat[1]).append("]");
                if (i < points.length - 1) {
                    coords.append(",");
                }
            }
        }

        coords.append("]");
        return coords.toString();
    }

    /**
     * 测试工具方法创建孔洞多边形
     */
    @Test
    public void testUtilityMethod() {
        String outerRing = "120.0,30.0;120.2,30.0;120.2,30.2;120.0,30.2;120.0,30.0";
        List<String> holes = Arrays.asList("120.05,30.05;120.15,30.05;120.15,30.15;120.05,30.15;120.05,30.05");

        String geoJson = createPolygonWithHoles(outerRing, holes);
        System.out.println("工具方法生成的GeoJSON: " + geoJson);

        CustomFenceAreaEsEntity entity = new CustomFenceAreaEsEntity();
        entity.setId("utility-method-test");
        entity.setAdCodeMsgId(1004);
        entity.setProvince("浙江省");
        entity.setCity("杭州市");
        entity.setArea("上城区");
        entity.setGeoShape(geoJson);
        entity.setStatus(0);

        try {
            customFenceAreaEsCommandRepository.saveAll(Arrays.asList(entity));
            System.out.println("✓ 工具方法测试成功");
        } catch (Exception e) {
            System.err.println("✗ 工具方法测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
