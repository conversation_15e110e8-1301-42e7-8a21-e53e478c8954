package net.summerfarm.wnc.repository;

import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CustomFenceAreaEsQueryRepositoryTest {

    @Resource
    private CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;

    @Test
    public void matchEsByAdCodeMsgIdsWithPoi() {
        Integer i = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(Arrays.asList(1), "120.130396,30.259242");
        System.out.println(i);
    }
}
