package net.summerfarm.wnc.inbound.provider.deliveryRule;

import net.summerfarm.wnc.client.req.deliveryRule.XmContactDeliveryRuleQueryReq;
import net.summerfarm.wnc.client.resp.deliveryRule.XmContactDeliveryRuleResp;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/4/16 18:09<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ContactDeliveryRuleQueryProviderImplTest {

    @Resource
    private ContactDeliveryRuleQueryProviderImpl contactDeliveryRuleQueryProvider;

    @Test
    public void queryXmContactDeliveryRuleTest(){
        XmContactDeliveryRuleQueryReq req = new XmContactDeliveryRuleQueryReq();
        req.setOutBusinessNo("123");
        DubboResponse<XmContactDeliveryRuleResp> xmContactDeliveryRuleRespDubboResponse = contactDeliveryRuleQueryProvider.queryXmContactDeliveryRule(req);
        System.out.println(xmContactDeliveryRuleRespDubboResponse);
    }
}
