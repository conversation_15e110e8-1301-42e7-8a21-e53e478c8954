package net.summerfarm.wnc.inbound.provider.warehouse;

import com.alibaba.fastjson.JSON;
import net.summerfarm.wnc.client.req.warehouse.PopWarehouseLogisticsQueryReq;
import net.summerfarm.wnc.client.resp.WarehousLogisticsCenterResp;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description: <br/>
 * date: 2024/6/19 15:47<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class WarehouseLogisticsQueryProviderImplTest {

    @Resource
    private WarehouseLogisticsQueryProviderImpl warehouseLogisticsQueryProvider;

    @Test
    public void queryIsSupportAddOrder() {
        DubboResponse<Boolean> booleanDubboResponse = warehouseLogisticsQueryProvider.queryIsSupportAddOrder(1);
        System.out.println(booleanDubboResponse);

        DubboResponse<Boolean> booleanDubboResponse2 = warehouseLogisticsQueryProvider.queryIsSupportAddOrder(219);
        System.out.println(booleanDubboResponse2);

        DubboResponse<List<WarehousLogisticsCenterResp>> listDubboResponse = warehouseLogisticsQueryProvider.queryPopWarehouseLogisticsList();
        System.out.println(listDubboResponse);

        PopWarehouseLogisticsQueryReq req = new PopWarehouseLogisticsQueryReq();
        req.setCity("杭州市");
        req.setArea("撒大声地");
        req.setBigClientId(123);
        req.setTenantId(0L);
        req.setPoi("123");
        DubboResponse<WarehousLogisticsCenterResp> resp = warehouseLogisticsQueryProvider.queryPopWarehouseLogistics(req);
        System.out.println(resp);
    }

    @Test
    public void queryPopWarehouseLogisticsList() {
        DubboResponse<List<WarehousLogisticsCenterResp>> listDubboResponse = warehouseLogisticsQueryProvider.queryPopWarehouseLogisticsList();
        System.out.println(listDubboResponse);
    }


    @Test
    public void queryPopWarehouseLogistics() {
        PopWarehouseLogisticsQueryReq req = new PopWarehouseLogisticsQueryReq();
        req.setCity("杭州市");
        req.setArea("西湖区");
        DubboResponse<WarehousLogisticsCenterResp> resp = warehouseLogisticsQueryProvider.queryPopWarehouseLogistics(req);
        System.out.println(JSON.toJSONString(resp));

        req.setCity("雅安市");
        req.setArea("雨城区");
        resp = warehouseLogisticsQueryProvider.queryPopWarehouseLogistics(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void querySharedDeliveryPopWarehouseLogistics() {
        DubboResponse<List<WarehousLogisticsCenterResp>> listDubboResponse = warehouseLogisticsQueryProvider.querySharedDeliveryPopWarehouseLogistics();
        System.out.println(JSON.toJSONString(listDubboResponse));
    }
}
