package net.summerfarm.wnc.inbound.scheduler;

import net.summerfarm.wnc.api.changeTask.service.FenceChangeTaskService;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2025/4/10 14:52<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class FenceChangeAreaHandleJobTest {


    @Resource
    private FenceChangeTaskService fenceChangeTaskService;

    @Test
    public void test(){
        fenceChangeTaskService.executeFenceChangeAreaHandle();
    }
}
