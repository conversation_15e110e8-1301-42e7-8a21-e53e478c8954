package net.summerfarm.wnc.inbound.provider;

import net.summerfarm.wnc.application.inbound.provider.warehouseMapping.WarehouseThirdMappingQueryProviderImpl;
import net.summerfarm.wnc.client.provider.warehouseMapping.WarehouseThirdMappingQueryProvider;
import net.summerfarm.wnc.client.req.warehouseMapping.WarehouseThirdMappingQueryReq;
import net.summerfarm.wnc.client.resp.warehouseMapping.WarehouseThirdMappingResp;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2025/6/11 15:33<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class WarehouseThirdMappingQueryProviderImplTest {

    @Resource
    private WarehouseThirdMappingQueryProviderImpl warehouseThirdMappingQueryProvider;

    @Test
    public void queryWarehouseThirdMapping() {
        WarehouseThirdMappingQueryReq req = new WarehouseThirdMappingQueryReq();
       /* req.setTenantId(2L);
        req.setOpenPlatformAppKey("12312asdas");*/
        req.setWarehouseNo(222);
        DubboResponse<WarehouseThirdMappingResp> warehouseThirdMappingRespDubboResponse = warehouseThirdMappingQueryProvider.queryWarehouseThirdMapping(req);

        System.out.println(warehouseThirdMappingRespDubboResponse);
    }
}
