package net.summerfarm.wnc.inbound.provider.fence;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.provider.fence.AdCodeMsgQueryProviderImpl;
import net.summerfarm.wnc.client.provider.fence.AdCodeMsgQueryProvider;
import net.summerfarm.wnc.client.req.fence.AdCodePageQueryReq;
import net.summerfarm.wnc.client.req.fence.StoreNoAddrStatusQueryReq;
import net.summerfarm.wnc.client.resp.fence.AdCodeMsgResp;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description: <br/>
 * date: 2024/11/15 11:01<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class AdCodeMsgQueryProviderTest {

    @Resource
    private AdCodeMsgQueryProviderImpl adCodeMsgQueryProvider;

    @Test
    public void queryAdCodeMsgTest(){
        AdCodePageQueryReq req = new AdCodePageQueryReq();
        req.setPageIndex(0);
        req.setPageSize(10);
        DubboResponse<PageInfo<AdCodeMsgResp>> pageInfoDubboResponse = adCodeMsgQueryProvider.queryAdCodeMsgPage(req);
        System.out.println(pageInfoDubboResponse);
    }

    @Test
    public void queryAdCodeMsgByStoreNoAddrStatusTest(){
        StoreNoAddrStatusQueryReq req = new StoreNoAddrStatusQueryReq();
        req.setStoreNo(1);
        req.setAdCodeMsgStatus(0);
        //req.setFenceStatus(0);
        //req.setWarehouseLogisticsCenterStatus(1);
        req.setCityLikeName("上海市");
        DubboResponse<List<AdCodeMsgResp>> listDubboResponse = adCodeMsgQueryProvider.queryAdCodeMsgByStoreNoAddrStatus(req);
        System.out.println(listDubboResponse);
    }
}
