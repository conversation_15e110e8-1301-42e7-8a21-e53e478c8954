package net.summerfarm.wnc.inbound.provider.closeTime;

import net.summerfarm.wnc.application.inbound.provider.closeTime.CloseTimeConfigCommandProviderImpl;
import net.summerfarm.wnc.client.req.closeTime.CloseTimeAreaAdjustCommandReq;
import net.summerfarm.wnc.client.req.closeTime.CloseTimeAreaConfigAdjustBatchCommandReq;
import net.summerfarm.wnc.starter.Application;
import org.apache.commons.collections.list.PredicatedList;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: 截单时间<br/>
 * date: 2024/3/22 11:41<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CloseTimeConfigCommandProviderImplTest {

    @Resource
    private CloseTimeConfigCommandProviderImpl closeTimeConfigCommandProvider;

    @Test
    public void testBatchQueryContactAddressBelongFence(){
        List<CloseTimeAreaAdjustCommandReq> commandReqs = new ArrayList<>();

        CloseTimeAreaAdjustCommandReq req = new CloseTimeAreaAdjustCommandReq();
        req.setProvince("浙江");
        req.setCity("杭州市");
        req.setArea("西湖区");
        req.setTenantId(2L);
        commandReqs.add(req);

        CloseTimeAreaAdjustCommandReq req2 = new CloseTimeAreaAdjustCommandReq();
        req2.setProvince("浙江");
        req2.setCity("杭州市");
        req2.setArea("拱墅区");
        req2.setTenantId(2L);
        commandReqs.add(req2);

        CloseTimeAreaConfigAdjustBatchCommandReq batchCommandReq = new CloseTimeAreaConfigAdjustBatchCommandReq();
        batchCommandReq.setCommandReqs(commandReqs);
        closeTimeConfigCommandProvider.batchAdjustCloseTimeAreaConfig(batchCommandReq);
    }
}
