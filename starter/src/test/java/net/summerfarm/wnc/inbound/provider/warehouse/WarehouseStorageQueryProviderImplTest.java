package net.summerfarm.wnc.inbound.provider.warehouse;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.client.req.XmWarehouseQueryReq;
import net.summerfarm.wnc.client.req.warehouse.WarehouseQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseStorageCenterResp;
import net.summerfarm.wnc.client.resp.warehouse.WarehouseStorageCenterBaseInfoResp;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Description: <br/>
 * date: 2024/7/8 13:39<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class WarehouseStorageQueryProviderImplTest {
    @Resource
    private WarehouseStorageQueryProviderImpl warehouseStorageQueryProvider;


    @Test
    public void queryXmWarehouseListTest(){
        XmWarehouseQueryReq req = new XmWarehouseQueryReq();
        req.setWarehouseNos(Arrays.asList(1,3));
        //req.setSelfWarehouseFlag(true);
        DubboResponse<List<WarehouseStorageCenterResp>> listDubboResponse = warehouseStorageQueryProvider.queryXmWarehouseList(req);
        System.out.println(listDubboResponse);
    }

    @Test
    public void queryWarehouseBaseInfoListTest(){
        WarehouseQueryReq req = new WarehouseQueryReq();
        req.setPageIndex(0);
        req.setPageSize(10);
        //req.setWarehouseNos(Arrays.asList(3));
        req.setSelfWarehouseFlag(false);
        DubboResponse<PageInfo<WarehouseStorageCenterBaseInfoResp>> pageInfoDubboResponse = warehouseStorageQueryProvider.queryWarehouseBaseInfoList(req);
        System.out.println(pageInfoDubboResponse);
    }

    @Test
    public void queryAllPopWarehouseList() {
        DubboResponse<List<WarehouseStorageCenterResp>> dubboResponse = warehouseStorageQueryProvider.queryAllPopWarehouseList();
        System.out.println(JSON.toJSONString(dubboResponse));
    }

}
