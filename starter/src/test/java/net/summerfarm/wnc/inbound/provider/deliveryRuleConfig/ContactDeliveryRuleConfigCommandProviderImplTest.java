package net.summerfarm.wnc.inbound.provider.deliveryRuleConfig;

import net.summerfarm.wnc.application.inbound.provider.deliveryRuleConfig.ContactDeliveryRuleConfigCommandProviderImpl;
import net.summerfarm.wnc.client.provider.deliveryRuleConfig.ContactDeliveryRuleConfigCommandProvider;
import net.summerfarm.wnc.client.req.deliveryRuleConfig.ContactConfigCommandReq;
import net.summerfarm.wnc.client.req.deliveryRuleConfig.DeliveryRuleCommandReq;
import net.summerfarm.wnc.client.req.deliveryRuleConfig.DeliveryRuleConfigSaveOrUpdateCommandReq;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * Description: <br/>
 * date: 2024/4/16 17:07<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ContactDeliveryRuleConfigCommandProviderImplTest {


    @DubboReference
    private ContactDeliveryRuleConfigCommandProvider contactDeliveryRuleConfigCommandProvider;

    @Test
    public void outerOrderDeliveryRuleConfigSaveOrUpdate() {
        DeliveryRuleConfigSaveOrUpdateCommandReq req = new DeliveryRuleConfigSaveOrUpdateCommandReq();
        req.setContactId(123L);
        req.setTenantId(2L);
        ContactConfigCommandReq configReq = new ContactConfigCommandReq();
        configReq.setStoreNo(2);
        req.setContactConfigCommandReq(configReq);

        DeliveryRuleCommandReq deliveryRuleReq = new DeliveryRuleCommandReq();
        deliveryRuleReq.setFrequentMethod(2);
        deliveryRuleReq.setDeliveryFrequentInterval(2);
        deliveryRuleReq.setBeginCalculateDate(LocalDate.now());
        req.setDeliveryRuleCommandReq(deliveryRuleReq);

        DubboResponse<Void> voidDubboResponse = contactDeliveryRuleConfigCommandProvider.outerOrderDeliveryRuleConfigSaveOrUpdate(req);
        System.out.println(voidDubboResponse);
    }

}
