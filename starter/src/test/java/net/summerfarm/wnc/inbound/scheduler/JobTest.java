package net.summerfarm.wnc.inbound.scheduler;

import net.summerfarm.wnc.domain.closeTime.CloseTimeConfigCommandDomainService;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: 定时任务<br/>
 * date: 2024/3/22 11:33<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class JobTest {

    @Resource
    private CloseTimeConfigCommandDomainService closeTimeConfigCommandDomainService;

    @Test
    public void test(){
        closeTimeConfigCommandDomainService.takeEffectAreaConfig();
    }
}
