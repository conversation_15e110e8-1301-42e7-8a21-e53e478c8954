package net.summerfarm.wnc.inbound.provider;

import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.client.req.DeliveryRuleQueryReq;
import net.summerfarm.wnc.client.resp.DeliveryRuleResp;
import net.summerfarm.wnc.inbound.provider.delivery.DeliveryRuleQueryProviderImpl;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * Description: 配送日期测试类<br/>
 * date: 2024/3/4 11:33<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class DeliveryRuleQueryProviderImplTest {

    @Resource
    private DeliveryRuleQueryProviderImpl deliveryRuleQueryProvider;

    @Test
    public void test(){
        DeliveryRuleQueryReq deliveryRuleQueryReq = new DeliveryRuleQueryReq();
        deliveryRuleQueryReq.setSource(SourceEnum.POP_MALL);
        deliveryRuleQueryReq.setOrderTime(LocalDateTime.now());
        deliveryRuleQueryReq.setContactId(351859L);
        deliveryRuleQueryReq.setMerchantId(349839L);
        deliveryRuleQueryReq.setSkus(Arrays.asList("299554374354"));
       /* deliveryRuleQueryReq.setQueryBeginDate(LocalDate.now().plusDays(2));
        deliveryRuleQueryReq.setQueryEndDate(LocalDate.now().plusDays(60));*/
        DubboResponse<DeliveryRuleResp> response = deliveryRuleQueryProvider.queryDeliveryDateInfo(deliveryRuleQueryReq);

        System.out.println(response);
    }
}
