package net.summerfarm.wnc.inbound.provider.closeTime;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.provider.closeTime.CloseTimeConfigQueryProviderImpl;
import net.summerfarm.wnc.client.req.closeTime.CloseTimeAreaConfigQueryReq;
import net.summerfarm.wnc.client.resp.closeTime.CloseTimeAreaConfigResp;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/3/22 15:31<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CloseTimeConfigQueryProviderImplTest {

    @Resource
    private CloseTimeConfigQueryProviderImpl closeTimeConfigQueryProvider;

    @Test
    public void testBatchQueryContactAddressBelongFence(){
        CloseTimeAreaConfigQueryReq req = new CloseTimeAreaConfigQueryReq();
        req.setTenantId(2L);
        req.setPageIndex(0);
        req.setPageSize(20);
        DubboResponse<PageInfo<CloseTimeAreaConfigResp>> pageInfoDubboResponse = closeTimeConfigQueryProvider.queryCloseTimeAreaConfigPage(req);

        System.out.println(pageInfoDubboResponse);
    }
}
