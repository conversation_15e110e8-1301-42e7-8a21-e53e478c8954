package net.summerfarm.wnc.inbound.provider.deliveryRule;

import net.summerfarm.wnc.client.req.deliveryRule.XmContactDeliveryRuleCommandReq;
import net.summerfarm.wnc.client.req.deliveryRule.XmContactDeliveryRuleDelCommandReq;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/4/17 13:57<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ContactDeliveryRuleCommandProviderImplTest {
    @Resource
    private ContactDeliveryRuleCommandProviderImpl contactDeliveryRuleCommandProvider;

    @Test
    public void xmContactDeliveryRuleSaveOrUpdateTest(){
        XmContactDeliveryRuleCommandReq req = new XmContactDeliveryRuleCommandReq();
        req.setOutBusinessNo("123");
        req.setFrequentMethod(1);
        req.setWeekDeliveryFrequent("0");
        contactDeliveryRuleCommandProvider.xmContactDeliveryRuleSaveOrUpdate(req);
    }

    @Test
    public void xmContactDeliveryRuleDeleteTest(){
        XmContactDeliveryRuleDelCommandReq req = new XmContactDeliveryRuleDelCommandReq();
        req.setOutBusinessNo("123");
        contactDeliveryRuleCommandProvider.xmContactDeliveryRuleDelete(req);
    }

}
