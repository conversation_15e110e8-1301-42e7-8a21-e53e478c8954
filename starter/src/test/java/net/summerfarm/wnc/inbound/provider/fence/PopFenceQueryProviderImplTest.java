package net.summerfarm.wnc.inbound.provider.fence;

import com.alibaba.fastjson.JSON;
import net.summerfarm.wnc.application.inbound.provider.fence.PopFenceQueryProviderImpl;
import net.summerfarm.wnc.client.req.fence.PopAddressAreaQueryReq;
import net.summerfarm.wnc.client.resp.fence.PopAreaResp;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/11/7 13:44<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class PopFenceQueryProviderImplTest {

    @Resource
    private PopFenceQueryProviderImpl popFenceQueryProvider;

    @Test
    public void queryPopAreaByAddressTest() {
        PopAddressAreaQueryReq req = new PopAddressAreaQueryReq();
        req.setCity("杭州市");
        req.setArea("西湖区");
        DubboResponse<PopAreaResp> response = popFenceQueryProvider.queryPopAreaByAddress(req);
        System.out.println(JSON.toJSONString(response));

        req.setCity("中山市");
        req.setArea("西湖区");
        DubboResponse<PopAreaResp> response2 = popFenceQueryProvider.queryPopAreaByAddress(req);
        System.out.println(JSON.toJSONString(response2));


        req.setCity("上海市");
        req.setArea("西湖区");
        DubboResponse<PopAreaResp> response3 = popFenceQueryProvider.queryPopAreaByAddress(req);
        System.out.println(JSON.toJSONString(response3));

        req.setCity("杭州市");
        req.setArea("临平区");
        DubboResponse<PopAreaResp> response4 = popFenceQueryProvider.queryPopAreaByAddress(req);
        System.out.println(JSON.toJSONString(response4));


        req.setCity("雅安市");
        req.setArea("雨城区");
        DubboResponse<PopAreaResp> response5 = popFenceQueryProvider.queryPopAreaByAddress(req);
        System.out.println(JSON.toJSONString(response5));
    }
}
