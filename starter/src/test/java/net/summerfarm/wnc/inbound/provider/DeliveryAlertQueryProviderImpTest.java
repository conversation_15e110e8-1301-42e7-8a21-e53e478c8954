package net.summerfarm.wnc.inbound.provider;

import net.summerfarm.wnc.application.inbound.provider.alert.DeliveryAlertQueryProviderImpl;
import net.summerfarm.wnc.client.provider.alert.req.SummerfarmDeliveryAlertTimeQueryReq;
import net.summerfarm.wnc.client.provider.alert.resp.SummerfarmDeliveryAlertTimeResp;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2024/3/29 15:40<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class DeliveryAlertQueryProviderImpTest {

    @Resource
    private DeliveryAlertQueryProviderImpl tmsDeliveryAlertQueryProvider;

    @Test
    public void querySummerfarmDeliveryAlertTime() {
        SummerfarmDeliveryAlertTimeQueryReq req = new SummerfarmDeliveryAlertTimeQueryReq();
        req.setStoreNo(291);
        req.setAdminId("");
        req.setMerchantId("342962");
        req.setCity("杭州市");
        req.setArea("余杭区");

        DubboResponse<SummerfarmDeliveryAlertTimeResp> respDubboResponse = tmsDeliveryAlertQueryProvider.querySummerfarmDeliveryAlertTime(req);
        System.out.println(respDubboResponse);
    }

}
