package net.summerfarm.wnc.inbound.provider;

import com.alibaba.fastjson.JSON;
import net.summerfarm.wnc.client.req.fence.ContactAddressBatchQueryReq;
import net.summerfarm.wnc.client.req.fence.ContactAddressQueryReq;
import net.summerfarm.wnc.client.req.path.BelowSkuPathMappingReq;
import net.summerfarm.wnc.client.req.path.SkuPathMappingDeleteReq;
import net.summerfarm.wnc.client.resp.fence.ContactAddressBelongFenceResp;
import net.summerfarm.wnc.client.resp.path.SkuPathMappingResp;
import net.summerfarm.wnc.inbound.provider.fence.DeliveryFenceQueryProviderImpl;
import net.summerfarm.wnc.inbound.provider.path.PathServiceCommandProviderImpl;
import net.summerfarm.wnc.inbound.provider.path.PathServiceQueryProviderImpl;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: <br/>
 * date: 2024/1/31 10:24<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class PathServiceQueryProviderImplTest {
    @Resource
    private PathServiceQueryProviderImpl pathServiceQueryProvider;
    @Resource
    private PathServiceCommandProviderImpl pathServiceCommandProvider;

    @Test
    public void testBatchQueryContactAddressBelongFence(){
        List<BelowSkuPathMappingReq> list = new ArrayList<>();
        BelowSkuPathMappingReq req = new BelowSkuPathMappingReq();
        req.setSku("50132414307");
        req.setSupWarehouseNo(1);
        list.add(req);
        BelowSkuPathMappingReq req2 = new BelowSkuPathMappingReq();
        req2.setSku("309804610031");
        req2.setSupWarehouseNo(1);
        list.add(req2);
        DubboResponse<List<SkuPathMappingResp>> listDubboResponse = pathServiceQueryProvider.queryBelowSkuPathMappingList(list);
        System.out.println(listDubboResponse);
    }

    @Test
    public void test(){
        List<SkuPathMappingDeleteReq> list = new ArrayList<>();
        SkuPathMappingDeleteReq req = new SkuPathMappingDeleteReq();
        req.setWarehouseNo(6666);
        req.setSku("321");
        list.add(req);
        SkuPathMappingDeleteReq req2 = new SkuPathMappingDeleteReq();
        req2.setWarehouseNo(123456);
        req2.setSku("321");
        list.add(req2);
        pathServiceCommandProvider.skuPathMappingBatchDeleteCommand(list);
    }
}
