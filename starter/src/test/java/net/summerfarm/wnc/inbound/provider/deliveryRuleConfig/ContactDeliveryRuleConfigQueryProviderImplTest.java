package net.summerfarm.wnc.inbound.provider.deliveryRuleConfig;

import jdk.nashorn.internal.ir.annotations.Reference;
import net.summerfarm.wnc.application.inbound.provider.deliveryRuleConfig.ContactDeliveryRuleConfigQueryProviderImpl;
import net.summerfarm.wnc.client.req.deliveryRuleConfig.DeliveryRuleConfigQueryReq;
import net.summerfarm.wnc.client.resp.deliveryRuleConfig.OrderDeliveryRuleConfigResp;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ContactDeliveryRuleConfigQueryProviderImplTest {

    @Resource
    private ContactDeliveryRuleConfigQueryProviderImpl contactDeliveryRuleConfigQueryProvider;

    @Test
    public void queryOuterOrderDeliveryRuleConfigTest() {
        DeliveryRuleConfigQueryReq req = new DeliveryRuleConfigQueryReq();
        req.setCity("杭州市");
        req.setArea("西湖区");
        req.setContactId(123L);
        req.setTenantId(2L);
        // Test
        DubboResponse<OrderDeliveryRuleConfigResp> response = contactDeliveryRuleConfigQueryProvider.queryOuterOrderDeliveryRuleConfig(req);
        System.out.println(response);
    }

}