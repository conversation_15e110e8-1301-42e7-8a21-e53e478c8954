<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>summerfarm-wnc</artifactId>
        <groupId>net.summerfarm</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>summerfarm-wnc-starter</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-common</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-facade</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-wnc-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-domain</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>summerfarm-wnc-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>summerfarm-wnc-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-infrastructure</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-wnc-domain</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-wnc-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-application</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-wnc-domain</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-wnc-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-wnc-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-inbound</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-wnc-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-wnc-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-log-support</artifactId>
        </dependency>

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-sentinel-support</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-mybatis-interceptor-support</artifactId>
        </dependency>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>net.summerfarm.wnc.starter.Application</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
