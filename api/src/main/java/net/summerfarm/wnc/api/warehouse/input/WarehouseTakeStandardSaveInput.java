package net.summerfarm.wnc.api.warehouse.input;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/29 18:48<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseTakeStandardSaveInput implements Serializable {

    /**
     * 0国产鲜果 1国产非鲜果 2进口鲜果 3进口非鲜果
     */
    private Integer standardType;

    /**
     * 证件信息
     */
    private List<StorageProveCommandInput> storageProve;
}
