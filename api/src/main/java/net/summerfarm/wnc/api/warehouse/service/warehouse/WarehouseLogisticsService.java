package net.summerfarm.wnc.api.warehouse.service.warehouse;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseLogisticsCenterDTO;
import net.summerfarm.wnc.api.warehouse.dto.CustomFenceSameCityAreaWarehouseLogisticsMappingDTO;
import net.summerfarm.wnc.api.warehouse.input.CustomFenceSameCityAreaMappingQueryInput;
import net.summerfarm.wnc.api.warehouse.input.LogisticsMappingDeleteInput;
import net.summerfarm.wnc.api.warehouse.input.WarehouseLogisticsSaveInput;
import net.summerfarm.wnc.api.warehouse.input.WarehouseLogisticsUpdateInput;
import net.summerfarm.wnc.common.query.warehouse.WarehouseLogisticsQuery;

import java.util.List;

/**
 * Description:城配仓
 * <AUTHOR> />
 */
public interface WarehouseLogisticsService {

    /**
     * 查询城配仓分页信息
     * @param warehouseLogisticsQuery 查询
     */
    PageInfo<WarehouseLogisticsCenterDTO> queryLogisticsList(WarehouseLogisticsQuery warehouseLogisticsQuery);

    /**
     * 根据主键ID获取城配仓详情信息
     * @param id 主键
     */
    WarehouseLogisticsCenterDTO queryLogisticsDetail(Integer id);

    /**
     * 更新城配仓信息
     * @param warehouseLogisticsUpdateCommand 更新信息
     */
    void updateLogistics(WarehouseLogisticsUpdateInput warehouseLogisticsUpdateCommand);

    /**
     * 删除配送中心使用库存仓
     * @param input 请求
     */
    void mappingDelete(LogisticsMappingDeleteInput input);

    /**
     * 新增城配仓信息
     * @param input 请求
     */
    void logisticsSave(WarehouseLogisticsSaveInput input);

    /**
     * 物流中心取消更改截单时间
     * @param storeNo 城配仓编号
     */
    void closeTimeCancel(Integer storeNo);

    /**
     * 查询所有的城配仓信息
     * @param status 状态
     * @return 结果
     */
    List<WarehouseLogisticsCenterDTO> queryLogisticsAll(Integer status);

    /**
     * 查询城配仓信息
     * @param query 查询
     * @return 结果
     */
    List<WarehouseLogisticsCenterDTO> queryWarehouseLogisticsList(WarehouseLogisticsQuery query);

    /**
     * 根据城配仓编号查询是否支持加单
     * @param storeNo 城配仓编号
     * @return 结果
     */
    Boolean queryIsSupportAddOrderByStoreNo(Integer storeNo);

    /**
     * 根据城配仓查询相同城市区域自定义围栏使用关系信息
     * @param input 请求
     * @return 结果
     */
    List<CustomFenceSameCityAreaWarehouseLogisticsMappingDTO> queryCustomFenceSameCityAreaWarehouseLogisticsMapping(CustomFenceSameCityAreaMappingQueryInput input);
}
