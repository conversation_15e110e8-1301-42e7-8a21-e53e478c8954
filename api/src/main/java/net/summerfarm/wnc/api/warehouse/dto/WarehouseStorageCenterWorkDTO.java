package net.summerfarm.wnc.api.warehouse.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Description: <br/>
 * date: 2023/8/29 16:31<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageCenterWorkDTO {

    private Long id;

    /**
     * 仓库业务属性id
     */
    private Long warehouseStorageCenterBusinessId;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库工作开始时间
     */
    private LocalTime workStartTime;

    /**
     * 仓库工作结束时间
     */
    private LocalTime workEndTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}
