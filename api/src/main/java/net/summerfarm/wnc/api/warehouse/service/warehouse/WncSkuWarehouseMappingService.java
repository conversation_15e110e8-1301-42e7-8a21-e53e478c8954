package net.summerfarm.wnc.api.warehouse.service.warehouse;

import net.summerfarm.wnc.api.warehouse.dto.SkuWarehouseMappingDTO;
import net.summerfarm.wnc.common.query.warehouse.SkuWarehouseMappingQuery;

import java.util.List;
import java.util.Map;

/**
 * Description:SKU库存仓映射接口
 * date: 2023/9/4 11:32
 *
 * <AUTHOR>
 */
public interface WncSkuWarehouseMappingService {

    /**
     * 查询SKU库存仓映射关系
     * @param skuWarehouseMappingQueries 查询
     * @return 结果
     */
    List<SkuWarehouseMappingDTO> querySkuWarehouseMappings(List<SkuWarehouseMappingQuery> skuWarehouseMappingQueries);

    /**
     * 根据SKU和城配仓编号返回映射Map
     * @param skus sku
     * @param storeNo 城配仓编号
     * @return 返回key sku#storeNo，value -> warehouseNo映射关系
     * //key -> sku+"#"+storeNo
     * //value -> warehouseNo
     */
    Map<String, Integer> queryMapping2SkuStoreNoMap(List<String> skus, Integer storeNo);
}
