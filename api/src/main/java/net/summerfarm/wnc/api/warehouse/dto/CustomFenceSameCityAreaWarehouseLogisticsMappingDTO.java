package net.summerfarm.wnc.api.warehouse.dto;

import lombok.Data;

import java.util.List;

/**
 * 城配仓库存仓映射
 * date: 2025/9/11 16:49<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomFenceSameCityAreaWarehouseLogisticsMappingDTO {

    /**
     * 围栏ID
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 围栏类型
     */
    private Integer fenceType;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 仓库名称
     */
    private List<String> warehouseNameList;

    /**
     * 仓库编号
     */
    private List<Integer> warehouseNoList;
}
