package net.summerfarm.wnc.api.warehouse.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:SKU库存仓数据转换对象
 * date: 2023/9/4 11:15
 *
 * <AUTHOR>
 */
@Data
public class SkuWarehouseMappingDTO implements Serializable {

    private static final long serialVersionUID = -4595985206804761974L;

    /**
     * sku
     */
    private String sku;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 库存仓名称
     */
    private String warehouseName;

}
