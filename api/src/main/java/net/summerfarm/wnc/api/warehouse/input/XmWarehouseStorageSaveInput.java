package net.summerfarm.wnc.api.warehouse.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/29 18:11<br/>
 *
 * <AUTHOR> />
 */
@Data
public class XmWarehouseStorageSaveInput implements Serializable {

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    @NotBlank(message = "仓库名称不能为空")
    private String warehouseName;

    /**
     * 仓库负责人
     */
    @NotNull(message = "仓库负责人不能为空")
    private Integer manageAdminId;

    /**
     * 仓库类型：0、本部仓 1、外部仓 2、合伙人仓
     */
    @NotNull(message = "仓库类型不能为空")
    private Integer type;

    /**
     * 仓库所属合伙人
     */
    private Integer areaManageId;

    /**
     * 开放状态：0、不开放 1、开放
     */
    @NotNull(message = "开放状态不能为空")
    private Integer status;
    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;
    /**
     * 仓库地址
     */
    private String address;

    /**
     * 高德poi
     */
    @NotBlank(message = "poi不能为空")
    private String poiNote;

    /**
     * 邮件接收人
     */
    private String mailToAddress;

    /**
     * 联系人
     */
    private String personContact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 创建人id
     */
    private Integer creator;

    /**
     * 产能
     */
    @NotNull(message = "产能不能为空")
    private BigDecimal capacity;

    /**
     * 预约提前期
     */
    @NotNull(message = "预约提前期不能为空")
    private Integer advanceDay;

    /**
     * 仓库作业时间
     */
    private List<WorkTimeInput> workTimeInputs;

    /**
     * 收货标准
     */
    private List<WarehouseTakeStandardSaveInput> warehouseTakeStandardSaveInputs;

    /**
     * 租户ID 鲜沐默认为0
     */
    private Long tenantId;

    /**
     * 仓库照片
     */
    private String warehousePic;
}
