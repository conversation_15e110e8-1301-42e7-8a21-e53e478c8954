package net.summerfarm.wnc.api.warehouse.service.warehouse;

import net.summerfarm.wnc.api.warehouse.dto.WncWarehouseStorageTenantDTO;
import net.summerfarm.wnc.common.query.warehouse.WncWarehouseStorageTenantQuery;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/6 11:10<br/>
 *
 * <AUTHOR> />
 */
public interface WncWarehouseStorageTenantService {
    /**
     * 仓库绑定租户信息
     * @param rsWarehouseStorageTenantDTO 报文
     */
    void bindingTenant(WncWarehouseStorageTenantDTO rsWarehouseStorageTenantDTO);
    /**
     * 仓库解绑租户信息
     * @param rsWarehouseStorageTenantDTO 报文
     */
    void unbindingTenant(WncWarehouseStorageTenantDTO rsWarehouseStorageTenantDTO);

    /**
     * 查询租户的绑定信息
     * @param rsWarehouseStorageTenantQuery 查询
     * @return 结果
     */
    List<WncWarehouseStorageTenantDTO> queryTenantWarehouseList(WncWarehouseStorageTenantQuery rsWarehouseStorageTenantQuery);
}
