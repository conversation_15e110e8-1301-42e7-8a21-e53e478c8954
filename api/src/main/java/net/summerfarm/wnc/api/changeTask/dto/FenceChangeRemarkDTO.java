package net.summerfarm.wnc.api.changeTask.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:切仓说明数据转换对象
 * date: 2023/8/24 14:27
 *
 * <AUTHOR>
 */
@Data
public class FenceChangeRemarkDTO implements Serializable {

    private static final long serialVersionUID = -3339287217483260921L;

    /**
     * 原库存使用仓
     */
    private List<String> oldWarehouses;

    /**
     * 新库存使用仓
     */
    private List<String> newWarehouses;

    /**
     * 原运营区域
     */
    private String oldArea;

    /**
     * 新运营区域
     */
    private String newArea;

    /**
     * 原城配仓
     */
    private String oldStore;

    /**
     * 新城配仓
     */
    private String newStore;

    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exeTime;

}
