package net.summerfarm.wnc.api.warehouse.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/29 18:11<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageSaveInput implements Serializable {
    /**
     * 仓库名称
     */
    @NotBlank(message = "仓库名称不能为空")
    private String warehouseName;

    /**
     * 仓库地址
     */
    @NotBlank(message = "仓库地址不能为空")
    private String address;

    /**
     * poi
     */
    @NotBlank(message = "poi不能为空")
    private String poiNote;

    /**
     * 仓库联系人
     */
    @NotBlank(message = "仓库负责人不能为空")
    private String personContact;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;
    /**
     * 开放状态：0、不开放 1、开放
     */
    @NotNull(message = "开放状态不能为空")
    private Integer status;

    /**
     * 租户ID 鲜沐默认为0
     */
    private Long tenantId;

    /**
     * 产能
     */
    @NotNull(message = "产能不能为空")
    private Long capacity;

    /**
     * 预约提前期
     */
    @NotNull(message = "预约提前期不能为空")
    private Integer advanceDay;

    /**
     * 收货标准
     */
    private List<WarehouseTakeStandardSaveInput> warehouseTakeStandardSaveInputs;

    /**
     * 配送范围
     */
    private List<WarehouseStorageFenceSaveInput> sendFenceList;

    /**
     * 操作人id 前段不需要传
     */
    private Integer createAdminId;

    /**
     * 操作人名称
     */
    private String operatorName;
}
