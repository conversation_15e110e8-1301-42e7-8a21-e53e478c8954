package net.summerfarm.wnc.api.warehouse.service.warehouse;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.fence.dto.WncWarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseBaseDTO;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.api.warehouse.input.*;
import net.summerfarm.wnc.client.req.WarehouseStorageFenceQueryReq;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageFenceQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/30 19:01<br/>
 *
 * <AUTHOR> />
 */
public interface WarehouseStorageCenterService {

    /**
     * 保存库存仓
     * @param warehouseStorageSaveCommandInput 保存
     */
    WarehouseStorageDTO warehouseStorageSave(WarehouseStorageSaveInput warehouseStorageSaveCommandInput);

    /**
     * 更新自营仓仓库信息
     * @param warehouseStorageUpdateInput 更新
     */
    void warehouseStorageUpdate(WarehouseStorageUpdateInput warehouseStorageUpdateInput);

    /**
     * 查询仓库详情信息
     * @param id 仓库id
     * @return
     */
    WarehouseStorageDTO queryDetail(Integer id);

    /**
     * 分页查询
     * @param warehouseStorageQueryInput 查询
     * @return 结果
     */
    PageInfo<WarehouseStorageDTO> queryPage(WarehouseStorageQuery warehouseStorageQueryInput);

    /**
     * 根据仓库编号查询仓库信息
     * @param warehouseNo 仓库编号
     * @return 仓库信息
     */
    WarehouseStorageDTO queryByWarehouseNo(Integer warehouseNo);

    /**
     * 查询仓库信息
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    List<WarehouseStorageDTO> queryList(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 查询
     * @param warehouseStorageQueryInput 查询
     * @return 结果
     */
    List<WarehouseStorageDTO> queryWarehouseList(WarehouseStorageQuery warehouseStorageQueryInput);

    /**
     * 查询仓库围栏列表
     * @param warehouseStorageFenceQueryInput 查询
     * @return 结果
     */
    PageInfo<WarehouseStorageDTO> queryFenceList(WarehouseStorageFenceQuery warehouseStorageFenceQueryInput);

    /**
     * 编辑配送区域
     * @param warehouseFenceEditInput 入参
     */
    void fenceEdit(WarehouseFenceEditInput warehouseFenceEditInput);

    /**
     * 根据仓库编号查询详情
     * @param warehouseNo 仓库编号
     * @return 结果
     */
    WarehouseStorageDTO detailWarehouseNo(Integer warehouseNo);

    /**
     * 根据sku集合和城市查询鲜沐仓库信息
     *
     * @param warehouseStorageQuery 查询
     * @return 仓库信息
     */
    List<WarehouseStorageDTO> queryXmWarehouseBySkuCity(WarehouseStorageQuery warehouseStorageQuery);


    /**
     * 根据库存仓编号批量查询库存仓基础信息
     *
     * @param warehouseNos 仓库编号集合
     * @return 结果
     */
    List<WarehouseBaseDTO> queryBaseInfoByNos(List<Integer> warehouseNos);

    /**
     * 更新鲜沐仓信息
     * @param xmWarehouseStorageUpdateInput 更新
     */
    void updateXmWarehouseStorage(XmWarehouseStorageUpdateInput xmWarehouseStorageUpdateInput);

    /**
     * 查询鲜沐仓库列表
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    PageInfo<WarehouseStorageDTO> queryXmWarehousePageList(WarehouseStorageQuery warehouseStorageQuery);

    /**
     *保存鲜沐仓库
     * @param xmWarehouseStorageSaveInput 仓库信息
     */
    void saveXmWarehouse(XmWarehouseStorageSaveInput xmWarehouseStorageSaveInput);

    /**
     * 查询仓库相关信息
     * @param warehouseStorageQuery 请求
     * @return 结果
     */
    List<WarehouseStorageDTO> queryWarehouseListByInfo(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 查询城配仓和sku对应的库存数据
     * @param warehouseStorageFenceQueryReq 查询
     * @return
     */
    List<WncWarehouseStorageFenceRuleDTO> queryWarehouseStorageStoreFence(WarehouseStorageFenceQueryReq warehouseStorageFenceQueryReq);


    /**
     * 鲜沐-三方仓所有的开放的仓库
     * @return 仓库结果
     */
    List<WarehouseStorageDTO> queryXmThirdAllWarehouseList();

    /**
     * 鲜沐仓库的查询
     * @param warehouseStorageQuery 仓库的查询
     * @return 结果
     */
    List<WarehouseStorageDTO> queryXmWarehouseList(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 查询所有的仓库信息
     * @param query 查询
     * @return
     */
    List<WarehouseStorageDTO> queryAllWarehouseList(WarehouseStorageQuery query);

    /**
     * 分页查询仓库基础信息
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    PageInfo<WarehouseStorageDTO> queryBasePage(WarehouseStorageQuery warehouseStorageQuery);

    /**
     * 查询仓库权限
     * @param adminId 登录人
     * @param tenantId 租户ID
     * @return 库存仓
     */
    List<Integer> queryWarehouseNosDataPermission(Integer adminId, Long tenantId);
}
