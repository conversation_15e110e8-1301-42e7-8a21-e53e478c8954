package net.summerfarm.wnc.api.deliveryRule.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/11/13 15:16<br/>
 *
 * <AUTHOR> />
 */
@Data
public class ContactDeliveryRuleDTO {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 外部业务编号
     */
    private String outBusinessNo;

    /**
     * 系统来源 0鲜沐商城
     */
    private Integer systemSource;

    /**
     * 周期方案 1周计算 2间隔计算
     */
    private Integer frequentMethod;

    /**
     * 周的配送周期 0每天 1周一依次
     */
    private String weekDeliveryFrequent;

    /**
     * 配送间隔周期
     */
    private Integer deliveryFrequentInterval;

    /**
     * 开始计算日期
     */
    private LocalDate beginCalculateDate;

    /**
     * 租户ID
     */
    private Long tenantId;

}
