package net.summerfarm.wnc.api.changeTask.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeRemarkDTO;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeTaskDTO;
import net.summerfarm.wnc.api.changeTask.dto.FenceChangeTaskOrderDTO;
import net.summerfarm.wnc.api.changeTask.input.FenceChangeTaskCommand;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskDetailIdQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskIdQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskOrderPageQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskPageQuery;

/**
 * Description:围栏切仓任务接口
 * date: 2023/8/24 15:14
 *
 * <AUTHOR>
 */
public interface FenceChangeTaskService {

    /**
     * 分页查询切仓任务列表
     * @param fenceChangeTaskPageQuery 查询
     * @return 结果
     */
    PageInfo<FenceChangeTaskDTO> queryTaskPage(FenceChangeTaskPageQuery fenceChangeTaskPageQuery);

    /**
     * 分页查询切仓任务订单列表
     * @param fenceChangeTaskOrderPageQuery 查询
     * @return 结果
     */
    PageInfo<FenceChangeTaskOrderDTO> queryTaskOrderPage(FenceChangeTaskOrderPageQuery fenceChangeTaskOrderPageQuery);

    /**
     * 查询切仓任务详情
     * @param fenceChangeTaskIdQuery 查询
     * @return 结果
     */
    FenceChangeTaskDTO queryTaskDetail(FenceChangeTaskIdQuery fenceChangeTaskIdQuery);

    /**
     * 新增切仓任务-获取切仓说明信息
     * @param fenceChangeTaskCommand 命令
     * @return 结果
     */
    FenceChangeRemarkDTO queryTaskChangeRemark(FenceChangeTaskCommand fenceChangeTaskCommand);

    /**
     * 新增切仓任务
     * @param fenceChangeTaskCommand 命令
     * @return 结果
     */
    void addTask(FenceChangeTaskCommand fenceChangeTaskCommand);

    /**
     * 取消切仓任务
     * @param fenceChangeTaskIdQuery 查询
     * @return 结果
     */
    void cancelTask(FenceChangeTaskIdQuery fenceChangeTaskIdQuery);

    /**
     * 执行围栏切仓区域处理
     */
    void executeFenceChangeAreaHandle();

    /**
     * 执行围栏切仓订单处理
     */
    void executeFenceChangeOrderHandle();

    /**
     * 执行围栏切仓失败订单处理
     */
    void executeFenceChangeFailOrderHandle();

    /**
     * 订单切仓
     * @param fenceChangeTaskDetailIdQuery 查询
     */
    void orderChange(FenceChangeTaskDetailIdQuery fenceChangeTaskDetailIdQuery);
}
