package net.summerfarm.wnc.api.warehouse.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/4/3 19:24<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageDTO {

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库负责人
     */
    private Integer manageAdminId;

    /**
     * 仓库类型：0、本部仓 1、外部仓 2、合伙人仓
     */
    private Integer type;

    /**
     * 仓库所属合伙人
     */
    private Integer areaManageId;

    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;

    /**
     * 仓库地址
     */
    private String address;

    /**
     * 高德poi
     */
    private String poiNote;

    /**
     * 邮件接收人
     */
    private String mailToAddress;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 联系人
     */
    private String personContact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 所属租户1鲜沐
     */
    private Long tenantId;
    /**
     * 仓库服务商
     */
    private String warehouseServiceName;
    /**
     * sku
     */
    private String sku;
    /**
     * 仓库业务表 产能 预约提前期
     */
    private WarehouseStorageCenterBusDTO warehouseStorageCenterBusDTO;

    /**
     * 仓库收货标准
     */
    private List<WarehouseTakeStandardDTO> warehouseTakeStandardDTOs;

    /**
     * 仓库围栏信息
     */
    private List<WncWarehouseStorageFenceDTO> rcWarehouseStorageFenceDTOs;

    /**
     * 鲜沐仓库负责人名称
     */
    private String manageAdminName;

    /**
     * 库位管理状态（0：未开启库位管理，1：已开启库位管理，2：开启中）
     */
    private String cabinetStatus = "0";

    /**
     * 仓库外部对接状态（0：未开启，1：已开启）
     */
    private String externalStatus = "0";

    /**
     * 仓库作业时间
     */
    List<WarehouseStorageCenterWorkDTO> warehouseStorageCenterWorkDTOs;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 仓库照片
     */
    private String warehousePic;

    /**
     * 是否自营仓仓库
     * ture 自营仓仓库 false 非自营仓仓库
     */
    private Boolean selfWarehouseFlag;
}
