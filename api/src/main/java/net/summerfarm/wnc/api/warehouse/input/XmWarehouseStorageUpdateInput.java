package net.summerfarm.wnc.api.warehouse.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/29 18:11<br/>
 *
 * <AUTHOR> />
 */
@Data
public class XmWarehouseStorageUpdateInput implements Serializable {
    /**
     * 主键、自增
     */
    @NotNull(message = "id不能为为空")
    private Integer id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    @NotNull(message = "manageAdminId不能为为空")
    private String warehouseName;

    /**
     * 仓库负责人
     */
    @NotNull(message = "manageAdminId不能为为空")
    private Integer manageAdminId;

    /**
     * 仓库类型：0、本部仓 1、外部仓 2、合伙人仓
     */
    @NotNull(message = "type不能为为空")
    private Integer type;

    /**
     * 仓库所属合伙人
     */
    private Integer areaManageId;

    /**
     * 开放状态：0、不开放 1、开放
     */
    @NotNull(message = "status不能为为空")
    private Integer status;
    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;
    /**
     * 仓库地址
     */
    private String address;

    /**
     * 高德poi
     */
    @NotBlank(message = "poiNote不能为为空")
    private String poiNote;

    /**
     * 邮件接收人
     */
    private String mailToAddress;

    /**
     * 联系人
     */
    private String personContact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 创建人id
     */
    private Integer creator;

    /**
     * 产能
     */
    @NotNull(message = "capacity不能为为空")
    private BigDecimal capacity;

    /**
     * 预约提前期
     */
    @NotNull(message = "advanceDay不能为为空")
    private Integer advanceDay;

    /**
     * 仓库照片
     */
    private String warehousePic;
    /**
     * 仓库作业时间
     */
    private List<WorkTimeInput> workTimeInputs;

    /**
     * 收货标准
     */
    private List<WarehouseTakeStandardSaveInput> warehouseTakeStandardSaveInputs;

    /**
     * 配送范围
     */
    private List<WarehouseStorageFenceSaveInput> sendFenceList;
}
