package net.summerfarm.wnc.api.deliveryRule.service;

import net.summerfarm.wnc.api.deliveryRule.dto.ContactDeliveryRuleDTO;
import net.summerfarm.wnc.api.deliveryRule.input.ContactDeliveryRuleDelCommand;
import net.summerfarm.wnc.api.deliveryRule.input.ContactDeliveryRuleSaveUpdateCommand;
import net.summerfarm.wnc.common.query.deliveryRule.ContactDeliveryRuleQuery;

/**
 * Description: <br/>
 * date: 2023/11/13 15:11<br/>
 *
 * <AUTHOR> />
 */
public interface ContactDeliveryRuleService {

    /**
     * 查询配送规则
     * @param query 查询
     * @return 结果
     */
    ContactDeliveryRuleDTO queryContactDeliveryRule(ContactDeliveryRuleQuery query);

    /**
     * 新增或者更新地址配送周期
     * @param command 报文
     */
    void saveOrUpdate(ContactDeliveryRuleSaveUpdateCommand command);

    /**
     * 删除客户配送规则
     * @param command 报文
     */
    void delteDeliveryRule(ContactDeliveryRuleDelCommand command);
}
