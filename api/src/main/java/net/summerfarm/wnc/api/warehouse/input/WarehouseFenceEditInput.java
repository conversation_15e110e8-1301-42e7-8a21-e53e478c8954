package net.summerfarm.wnc.api.warehouse.input;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/30 18:44<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseFenceEditInput implements Serializable {

    /**
     *仓库Id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 配送区域集合
     */
    private List<WarehouseStorageFenceSaveInput> fenceList;

    /**
     * 操作人名称 不需要传
     */
    private String operatorName;
}
