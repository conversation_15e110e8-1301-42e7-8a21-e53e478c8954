package net.summerfarm.wnc.api.warehouse.dto;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/4/6 11:12<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WncWarehouseStorageTenantDTO {

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

}
