package net.summerfarm.wnc.api.warehouse.input;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Description: 城配仓和库存仓映射关系删除
 * date: 2023/10/9 15:16<br/>
 *
 * <AUTHOR> />
 */
@Data
public class LogisticsMappingDeleteInput {

    /**
     * 城配仓编号
     */
    @NotNull(message = "storeNo不能为空")
    private Integer storeNo;

    /**
     * 库存仓编号
     */
    @NotNull(message = "warehouseNo不能为空")
    private Integer warehouseNo;
}
