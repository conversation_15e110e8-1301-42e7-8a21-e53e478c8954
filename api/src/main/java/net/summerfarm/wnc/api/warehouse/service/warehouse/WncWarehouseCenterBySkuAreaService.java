package net.summerfarm.wnc.api.warehouse.service.warehouse;

import net.summerfarm.wnc.api.warehouse.dto.WarehouseBySkuAreaNoDTO;
import net.summerfarm.wnc.common.query.warehouse.WarehouseBySkuAreaNoQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-07-04
 **/
public interface WncWarehouseCenterBySkuAreaService {

	/**
	 * 通过sku和区域查询库存仓信息
	 *
	 * @param query
	 * @return
	 */
	List<WarehouseBySkuAreaNoDTO> queryBySkuArea(List<WarehouseBySkuAreaNoQuery> query);
}
