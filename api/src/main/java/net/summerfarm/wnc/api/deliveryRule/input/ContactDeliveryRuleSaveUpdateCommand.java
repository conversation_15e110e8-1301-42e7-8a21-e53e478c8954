package net.summerfarm.wnc.api.deliveryRule.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * Description: 门店配送规则新增更新<br/>
 * date: 2023/11/14 11:31<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactDeliveryRuleSaveUpdateCommand {

    /**
     * 外部业务编号
     */
    private String outBusinessNo;
    /**
     * 周期方案 1周计算 2间隔计算
     */
    private Integer frequentMethod;
    /**
     * 周的配送周期 0每天 1周一 依次,多个逗号分隔
     */
    private String weekDeliveryFrequent;

    /**
     * 配送间隔周期
     */
    private Integer deliveryFrequentInterval;

    /**
     * 开始计算日期
     */
    private LocalDate beginCalculateDate;

    /**
     * 系统来源 0鲜沐商城
     */
    private Integer systemSource;

    /**
     * 租户ID
     */
    private Long tenantId;
}
