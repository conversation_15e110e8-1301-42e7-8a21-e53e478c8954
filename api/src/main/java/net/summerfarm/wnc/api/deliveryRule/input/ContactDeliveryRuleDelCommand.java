package net.summerfarm.wnc.api.deliveryRule.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * Description: 门店配送规则删除<br/>
 * date: 2023/11/14 11:31<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactDeliveryRuleDelCommand {

    /**
     * 外部业务编号
     */
    private String outBusinessNo;

    /**
     * 系统来源 0鲜沐商城 1Saas
     */
    private Integer systemSource;

    /**
     * 租户ID
     */
    private Long tenantId;
}
