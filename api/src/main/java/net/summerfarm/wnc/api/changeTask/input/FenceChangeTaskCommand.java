package net.summerfarm.wnc.api.changeTask.input;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Description:切仓任务更新命令
 * date: 2023/8/23 11:56
 *
 * <AUTHOR>
 */
@Data
public class FenceChangeTaskCommand implements Serializable {

    private static final long serialVersionUID = -7805349429502528434L;

    /**
     * 原围栏ID
     */
    @NotNull(message = "原围栏ID不能为空")
    private Integer fenceId;

    /**
     * 类型,0:切仓,1:切围栏
     */
    @NotNull(message = "切仓类型不能为空")
    private Integer type;

    /**
     * 目标围栏ID/目标城配仓编号
     */
    @NotNull(message = "目标编号不能为空")
    private Integer targetNo;

    /**
     * 切换区域集合
     */
    @NotEmpty(message = "切换区域不能为空")
    private List<Integer> changeAcmIds;
}
