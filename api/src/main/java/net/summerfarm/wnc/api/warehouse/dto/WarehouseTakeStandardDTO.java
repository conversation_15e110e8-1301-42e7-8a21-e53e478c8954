package net.summerfarm.wnc.api.warehouse.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/4/3 19:26<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseTakeStandardDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 0国产鲜果 1国产非鲜果 2进口鲜果 3进口非鲜果
     */
    private Integer standardType;

    /**
     * 仓储区域
     */
    private Integer storageLocation;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 证明类型：0质检报告 1农残检测报告 2消毒记录 3核酸检测 4报关证明 5监管仓证明
     */
    private Integer proveType;

    /**
     * 创建人id
     */
    private Integer createAdminId;
}
