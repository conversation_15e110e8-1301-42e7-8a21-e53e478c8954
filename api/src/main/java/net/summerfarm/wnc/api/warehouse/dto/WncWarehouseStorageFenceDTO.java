package net.summerfarm.wnc.api.warehouse.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/4/3 19:26<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WncWarehouseStorageFenceDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 所属租户
     */
    private Long tenantId;

    /**
     * 最后操作人名称
     */
    private String lastOperatorName;

    /**
     * sku 信息
     */
    private String sku;

}
