package net.summerfarm.wnc.api.warehouse.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/4/3 19:25<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageCenterBusDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 产能
     */
    private Long capacity;

    /**
     * 预约提前期
     */
    private Integer advanceDay;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;
}
