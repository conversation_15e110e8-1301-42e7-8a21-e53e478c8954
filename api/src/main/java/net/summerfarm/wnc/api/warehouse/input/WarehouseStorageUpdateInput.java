package net.summerfarm.wnc.api.warehouse.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/29 18:11<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseStorageUpdateInput implements Serializable {
    /**
     * id
     */
    @NotNull(message = "id不能为为空")
    private Integer id;
    /**
     * 开放状态：0、不开放 1、开放
     */
    private Integer status;
    /**
     * 仓库地址
     */
    private String address;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * poi
     */
    private String poiNote;

    /**
     * 仓库联系人
     */
    private String personContact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 租户ID 鲜沐默认为0
     */
    private Long tenantId;

    /**
     * 产能
     */
    private Long capacity;

    /**
     * 预约提前期
     */
    private Integer advanceDay;

    /**
     * 收货标准
     */
    private List<WarehouseTakeStandardSaveInput> warehouseTakeStandardSaveInputs;

    /**
     * 配送范围
     */
    private List<WarehouseStorageFenceSaveInput> sendFenceList;
    /**
     * 创建人id
     */
    private Long createAdminId;
}
