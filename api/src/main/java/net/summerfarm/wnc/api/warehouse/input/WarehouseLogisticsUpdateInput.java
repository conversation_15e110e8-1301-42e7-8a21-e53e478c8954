package net.summerfarm.wnc.api.warehouse.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: 城配仓更新
 * date: 2023/10/8 17:01<br/>
 *
 * <AUTHOR> />
 */
@Data
public class WarehouseLogisticsUpdateInput {
    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空")
    private Integer id;
    /**
     * 名称
     */
    @NotBlank(message = "城配仓名称不能为空")
    private String storeName;

    /**
     * 城配仓负责人
     */
    @NotNull(message = "城配仓负责人Id不能为空")
    private Integer manageAdminId;

    /**
     * 区域
     */
    @NotBlank(message = "区域不能为空")
    private String region;

    /**
     * 更新截单时间
     */
    @NotBlank(message = "更新截单时间不能为空")
    private String updateCloseTime;

    /**
     * 配送中心状态：0、失效 1、有效
     */
    @NotNull(message = "开放状态不能为空")
    private Integer status;

    /**
     * 是否需要打卡0 不需要 1需要
     */
    private Integer punchState;

    /**
     * 是否支持提前截单：0、否 1、是
     */
    @NotNull(message = "是否支持提前截单不能为空")
    private Integer closeOrderType;

    /**
     * 打卡距离
     */
    private BigDecimal punchDistance;

    /**
     * 城配仓地址
     */
    @NotBlank(message = "城配仓地址不能为空")
    private String address;

    /**
     * 城配仓poi
     */
    @NotBlank(message = "城配仓poi不能为空")
    private String poiNote;

    /**
     * 出仓时间 hh:mm:ss
     */
    @NotBlank(message = "出仓时间不能为空")
    private String outTime;

    /**
     * 库存使用仓
     */
    private List<Integer> warehouseNos;

    /**
     * 手动排线 0智能排线 1手动排线
     */
    @NotNull(message = "手动排线不能为空")
    private Integer intelligencePath;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空")
    private String personContact;

    /**
     * 联系方式
     */
    @NotBlank(message = "联系方式不能为空")
    private String phone;

    /**
     * 城配仓照片
     */
    private String storePic;

    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    @NotNull(message = "履约类型不能为空")
    private Integer fulfillmentType;
}
