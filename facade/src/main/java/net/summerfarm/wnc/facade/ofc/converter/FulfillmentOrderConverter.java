package net.summerfarm.wnc.facade.ofc.converter;

import net.summerfarm.ofc.client.resp.fulfillment.FulfillmentOrderResp;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;

/**
 * Description:履约单转换器
 * date: 2023/8/31 16:11
 *
 * <AUTHOR>
 */
public class FulfillmentOrderConverter {

    public static FulfillmentOrderDTO resp2dto(FulfillmentOrderResp fulfillmentOrderResp){
        if (fulfillmentOrderResp == null){
            return null;
        }
        FulfillmentOrderDTO fulfillmentOrderDTO = new FulfillmentOrderDTO();
        fulfillmentOrderDTO.setOuterOrderId(fulfillmentOrderResp.getOutOrderNo());
        //枚举与OFC定义一致
        fulfillmentOrderDTO.setSource(fulfillmentOrderResp.getOrderSource());
        fulfillmentOrderDTO.setOuterContactId(fulfillmentOrderResp.getContactId());
        fulfillmentOrderDTO.setDeliveryTime(fulfillmentOrderResp.getDeliveryDate());
        fulfillmentOrderDTO.setStoreNo(fulfillmentOrderResp.getStoreNo());
        fulfillmentOrderDTO.setOuterClientId(fulfillmentOrderResp.getOuterClientId());
        fulfillmentOrderDTO.setOuterClientName(fulfillmentOrderResp.getOuterClientName());
        fulfillmentOrderDTO.setFulfillConfirmTime(fulfillmentOrderResp.getCreateTime());
        fulfillmentOrderDTO.setPoi(fulfillmentOrderResp.getPoi());
        fulfillmentOrderDTO.setCity(fulfillmentOrderResp.getCity());
        fulfillmentOrderDTO.setArea(fulfillmentOrderResp.getArea());

        return fulfillmentOrderDTO;
    }
}
