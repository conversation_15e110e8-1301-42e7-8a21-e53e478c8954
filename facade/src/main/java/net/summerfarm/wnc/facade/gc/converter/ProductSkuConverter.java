package net.summerfarm.wnc.facade.gc.converter;

import net.summerfarm.goods.client.resp.ProductSkuBasicResp;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDTO;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDetailDTO;

/**
 * Description: <br/>
 * date: 2023/9/27 14:28<br/>
 *
 * <AUTHOR> />
 */
public class ProductSkuConverter {

    public static ProductSkuDTO productSkuBaseResp2DTO(ProductSkuBasicResp productSkuBaseResp){
        if(productSkuBaseResp == null){
            return null;
        }

        ProductSkuDTO productSkuDTO = new ProductSkuDTO();

        productSkuDTO.setSku(productSkuBaseResp.getSku());
        productSkuDTO.setSubType(productSkuBaseResp.getSubAgentType());

        return productSkuDTO;
    }

    public static ProductSkuDetailDTO productSkuDetail2DetailDTO(ProductSkuDetailResp detailResp){
        if(detailResp == null){
            return null;
        }

        ProductSkuDetailDTO dto = new ProductSkuDetailDTO();
        dto.setSkuId(detailResp.getSkuId());
        dto.setTenantId(detailResp.getTenantId());
        dto.setSpuId(detailResp.getSpuId());
        dto.setSku(detailResp.getSku());
        dto.setAgentType(detailResp.getAgentType());
        dto.setSubAgentType(detailResp.getSubAgentType());
        dto.setPlaceType(detailResp.getPlaceType());
        dto.setVolumeUnit(detailResp.getVolumeUnit());
        dto.setVolume(detailResp.getVolume());
        dto.setSpecification(detailResp.getSpecification());
        dto.setAssociated(detailResp.getAssociated());
        dto.setWeightNotes(detailResp.getWeightNotes());
        dto.setWeight(detailResp.getWeight());
        dto.setSpecificationUnit(detailResp.getSpecificationUnit());
        dto.setSpecificationType(detailResp.getSpecificationType());
        dto.setCreateTime(detailResp.getCreateTime());
        dto.setUpdateTime(detailResp.getUpdateTime());
        dto.setTaxRateValue(detailResp.getTaxRateValue());
        dto.setCustomSkuCode(detailResp.getCustomSkuCode());
        dto.setUseFlag(detailResp.getUseFlag());
        dto.setCreateType(detailResp.getCreateType());
        dto.setSkuPicture(detailResp.getSkuPicture());
        dto.setSkuTitle(detailResp.getSkuTitle());
        dto.setOwnerId(detailResp.getOwnerId());
        dto.setFirstCategory(detailResp.getFirstCategory());
        dto.setFirstCategoryId(detailResp.getFirstCategoryId());
        dto.setSecondCategory(detailResp.getSecondCategory());
        dto.setSecondCategoryId(detailResp.getSecondCategoryId());
        dto.setCategoryId(detailResp.getCategoryId());
        dto.setCategoryName(detailResp.getCategoryName());
        dto.setCategoryType(detailResp.getCategoryType());
        dto.setTitle(detailResp.getTitle());
        dto.setSubTitle(detailResp.getSubTitle());
        dto.setMainPicture(detailResp.getMainPicture());
        dto.setDetailPicture(detailResp.getDetailPicture());
        dto.setStorageLocation(detailResp.getStorageLocation());
        dto.setStorageTemperature(detailResp.getStorageTemperature());
        dto.setGuaranteePeriod(detailResp.getGuaranteePeriod());
        dto.setGuaranteeUnit(detailResp.getGuaranteeUnit());
        dto.setOrigin(detailResp.getOrigin());
        dto.setBrandName(detailResp.getBrandName());
        dto.setCustomSpuCode(detailResp.getCustomSpuCode());
        dto.setSkuMapping(detailResp.getSkuMapping());
        dto.setBasicSpecUnit(detailResp.getBasicSpecUnit());
        dto.setExtType(detailResp.getExtType());
        dto.setMaterialType(detailResp.getMaterialType());
        dto.setBuyerId(detailResp.getBuyerId());
        dto.setBuyerName(detailResp.getBuyerName());
        dto.setNetWeightNum(detailResp.getNetWeightNum());
        dto.setNetWeightUnit(detailResp.getNetWeightUnit());

        return dto;
    }
}
