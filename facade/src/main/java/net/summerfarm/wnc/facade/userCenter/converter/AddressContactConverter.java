package net.summerfarm.wnc.facade.userCenter.converter;

import net.summerfarm.wnc.facade.userCenter.dto.AddressContactDTO;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;

/**
 * Description:地址联系人转换器
 * date: 2023/12/4 16:41
 *
 * <AUTHOR>
 */
public class AddressContactConverter {

    public static AddressContactDTO resp2dto(MerchantContactResultResp merchantContactResultResp){
        if(merchantContactResultResp == null ){
            return null;
        }
        AddressContactDTO addressDTO = new AddressContactDTO();
        addressDTO.setId(merchantContactResultResp.getId());
        addressDTO.setAddressId(merchantContactResultResp.getAddressId());
        addressDTO.setName(merchantContactResultResp.getName());
        addressDTO.setPhone(merchantContactResultResp.getPhone());
        return addressDTO;
    }
}
