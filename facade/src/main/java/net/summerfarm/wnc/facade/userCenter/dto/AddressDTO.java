package net.summerfarm.wnc.facade.userCenter.dto;

import lombok.Data;

import java.util.List;

/**
 * Description:地址
 * date: 2023/12/1 15:17
 *
 * <AUTHOR>
 */
@Data
public class AddressDTO {

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * mId
     */
    private Long mId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 门店联系人集合
     */
    private List<AddressContactDTO> addressContactDTOList;
}
