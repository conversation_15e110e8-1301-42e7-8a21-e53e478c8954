package net.summerfarm.wnc.facade.userCenter.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description:地址查询
 * date: 2023/12/1 15:19
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressInput {

    /**
     * 门店ID
     */
    private Long storeId;
    /**
     * 门店ID集合
     */
    private List<Long> storeIds;
    /**
     * 城市
     */
    private String city;
    /**
     * 区域集合
     */
    private List<String> areas;
    /**
     * 联系人ID集合
     */
    private List<Long> contactIds;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 状态
     */
    private Integer status;
}
