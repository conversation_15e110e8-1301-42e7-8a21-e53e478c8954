package net.summerfarm.wnc.facade.auth;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.admin.AdminQueryProvider;
import net.summerfarm.client.req.admin.AdminQueryReq;
import net.summerfarm.client.resp.admin.AdminResp;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.provider.AuthBaseUserProvider;
import net.xianmu.authentication.client.resp.AuthUserBaseResp;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.security.ProviderException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/5/30 16:55<br/>
 *
 * <AUTHOR> />
 */
@Component
@Slf4j
public class AuthQueryFacade {

    @DubboReference
    private AuthBaseUserProvider authBaseUserProvider;
    @DubboReference
    private AdminQueryProvider adminQueryProvider;

    public String queryAdminRealName(Long adminId){
        AuthUserQueryInput authUserQueryInput = new AuthUserQueryInput();
        authUserQueryInput.setBizId(adminId);
        DubboResponse<AuthUserBaseResp> authUserBaseRespDubboResponse = null;
        try {
            authUserBaseRespDubboResponse = authBaseUserProvider.queryRealName(SystemOriginEnum.ADMIN, authUserQueryInput);
        } catch (Exception e) {
            log.error("dubbo请求查询Auth操作人名称异常adminId:{}",adminId,e);
            throw new TmsRuntimeException("请求Auth获取操作人名称异常");
        }
        if(authUserBaseRespDubboResponse == null || !authUserBaseRespDubboResponse.isSuccess() || authUserBaseRespDubboResponse.getData() == null){
            log.error("dubbo请求查询Auth操作人名称异常,adminId为:{}，返回信息{}",adminId, JSON.toJSONString(authUserBaseRespDubboResponse));
            throw new TmsRuntimeException("请求Auth获取操作人名称异常");
        }
        return authUserBaseRespDubboResponse.getData() == null ? "" : authUserBaseRespDubboResponse.getData().getName();
    }

    public Map<Integer,String> batchQueryAdmins(List<Integer> adminIds){
        if(CollectionUtils.isEmpty(adminIds)){
            return Collections.emptyMap();
        }
        AdminQueryReq adminQueryReq = new AdminQueryReq();
        adminQueryReq.setAdminIds(adminIds);
        DubboResponse<List<AdminResp>> resp = adminQueryProvider.batchQueryAdmins(adminQueryReq);
        if(resp == null || !resp.isSuccess()){
            log.error("dubbo请求查询Auth操作人名称异常,adminIds为:{}，返回信息{}",adminIds, JSON.toJSONString(resp));
            throw new ProviderException("请求Auth获取操作人名称异常");
        }
        List<AdminResp> adminResps = resp.getData();
        if(CollectionUtils.isEmpty(adminResps)){
            return Collections.emptyMap();
        }
        return adminResps.stream().collect(Collectors.toMap(AdminResp::getAdminId,AdminResp::getRealName));
    }
}
