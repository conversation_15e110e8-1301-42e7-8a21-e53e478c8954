package net.summerfarm.wnc.facade.tms;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.client.dist.provider.TmsDistSiteQueryProvider;
import net.summerfarm.tms.client.dist.req.DistSiteQueryReq;
import net.summerfarm.tms.client.dist.resp.DistSiteResp;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import net.summerfarm.wnc.facade.tms.converter.SiteConverter;
import net.summerfarm.wnc.facade.tms.dto.SiteDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 鲜沐TMS查询
 * <AUTHOR> />
 */
@Slf4j
@Component
public class TmsQueryFacade {

    @DubboReference
    TmsDistSiteQueryProvider tmsDistSiteQueryProvider;

    /**
     * 批量查询TMS城配仓点位信息
     * @return 结果
     */
    public List<SiteDTO> queryStoreSiteList(){
        DistSiteQueryReq distSiteQueryReq = new DistSiteQueryReq();
        distSiteQueryReq.setType(TmsSiteTypeEnum.STORE.getCode());
        DubboResponse<List<DistSiteResp>> dubboResponse = tmsDistSiteQueryProvider.queryDistSite(distSiteQueryReq);
        if (Objects.nonNull(dubboResponse) && dubboResponse.getCode().equals(DubboResponse.COMMON_SUCCESS_CODE)){
            return dubboResponse.getData().stream().map(SiteConverter::resp2DTO).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
