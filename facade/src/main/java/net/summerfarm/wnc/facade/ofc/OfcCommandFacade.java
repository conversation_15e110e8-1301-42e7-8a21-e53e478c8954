package net.summerfarm.wnc.facade.ofc;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.provider.FulfillmentOrderOperateProvider;
import net.summerfarm.ofc.client.req.fulfillment.UpdateOrderStoreNoReq;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentChangeCommandInput;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;


/**
 * Description:OFC操作Facade
 * date: 2023/8/31 15:06
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OfcCommandFacade {

    @DubboReference
    private FulfillmentOrderOperateProvider fulfillmentOrderOperateProvider;

    /**
     * 更新履约单履约仓
     * @param input 履约单变更输入参数
     */
    public void updateStoreNo(FulfillmentChangeCommandInput input){
        UpdateOrderStoreNoReq req = new UpdateOrderStoreNoReq();
        req.setOutOrderNo(input.getOuterOrderId());
        req.setContactId(input.getOuterContactId());
        req.setDeliveryDate(input.getDeliveryTime());
        req.setOfcOrderSource(input.getSource());
        req.setSourceStoreNo(input.getOldStoreNo());
        req.setNewStoreNo(input.getNewStoreNo());
        req.setOperatorName(input.getCreator());
        req.setFenceId(input.getFenceId());
        req.setFenceName(input.getFenceName());
        req.setAdCodeMsgId(input.getAdCodeMsgId());
        req.setCustomAreaName(input.getCustomAreaName());
        DubboResponse<Boolean> response = fulfillmentOrderOperateProvider.updateStoreNo(req);
        if (response == null){
            throw new ProviderException("调用OFC接口查询履约单数据异常");
        }
        if (!response.isSuccess()){
            throw new ProviderException(response.getMsg());
        }

    }
}
