package net.summerfarm.wnc.facade.mall.converter;

import net.summerfarm.client.resp.area.AreaSimpleResp;
import net.summerfarm.wnc.facade.mall.dto.AreaDTO;

/**
 * Description:运营服务区转换器
 * date: 2023/12/8 19:35
 *
 * <AUTHOR>
 */
public class AreaConverter {

    public static AreaDTO resp2dto(AreaSimpleResp areaSimpleResp){
        if (areaSimpleResp == null){
            return null;
        }
        AreaDTO areaDTO = new AreaDTO();
        areaDTO.setAreaNo(areaSimpleResp.getAreaNo());
        areaDTO.setAreaName(areaSimpleResp.getAreaName());
        areaDTO.setAreaStatus(areaSimpleResp.getAreaStatus());
        areaDTO.setLargeAreaNo(areaSimpleResp.getLargeAreaNo());
        areaDTO.setLargeAreaName(areaSimpleResp.getLargeAreaName());
        areaDTO.setLargeAreaStatus(areaSimpleResp.getLargeAreaStatus());
        return areaDTO;
    }
}
