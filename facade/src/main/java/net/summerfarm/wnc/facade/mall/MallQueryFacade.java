package net.summerfarm.wnc.facade.mall;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.area.AreaQueryProvider;
import net.summerfarm.client.resp.area.AreaSimpleResp;
import net.summerfarm.wnc.facade.mall.converter.AreaConverter;
import net.summerfarm.wnc.facade.mall.dto.AreaDTO;
import net.summerfarm.wnc.facade.mall.input.AreaInput;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:商城查询
 * date: 2023/12/8 19:15
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MallQueryFacade {

    @DubboReference
    private AreaQueryProvider areaQueryProvider;

    public List<AreaDTO> queryAreas(AreaInput areaInput) {
        DubboResponse<List<AreaSimpleResp>> response = areaQueryProvider.batchQueryByAreaNos(areaInput.getAreaNos());
        if (response == null || !response.isSuccess()){
            throw new ProviderException("调用商城获取运营服务区域信息异常");
        }
        return response.getData().stream().map(AreaConverter::resp2dto).collect(Collectors.toList());
    }
}
