package net.summerfarm.wnc.facade.ofc.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * Description:履约单查询输入参数
 * date: 2023/8/31 15:41
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FulfillmentQueryInput implements Serializable {

    private static final long serialVersionUID = 8092844099480083653L;

    /**
     * 行政市
     */
    private String city;

    /**
     * 行政区域集合
     */
    private List<String> areas;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 开始配送日期
     */
    private LocalDate deliveryDateBegin;
}
