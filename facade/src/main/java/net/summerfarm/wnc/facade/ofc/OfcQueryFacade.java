package net.summerfarm.wnc.facade.ofc;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.provider.FulfillmentOrderQueryProvider;
import net.summerfarm.ofc.client.req.fulfillment.FutureWaitFulfillmentQueryReq;
import net.summerfarm.ofc.client.resp.fulfillment.FulfillmentOrderResp;
import net.summerfarm.wnc.facade.ofc.converter.FulfillmentOrderConverter;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:OFC查询Facade
 * date: 2023/8/31 15:05
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OfcQueryFacade {

    @DubboReference
    private FulfillmentOrderQueryProvider fulfillmentOrderQueryProvider;

    /**
     * 查询OFC订单拦截
     * @return 返回拦截订单信息
     */
    public List<FulfillmentOrderDTO> queryTimePlus2WaitFulfillmentOrder(FulfillmentQueryInput fulfillmentQueryInput){
        FutureWaitFulfillmentQueryReq req = new FutureWaitFulfillmentQueryReq();
        req.setCity(fulfillmentQueryInput.getCity());
        req.setAreaList(fulfillmentQueryInput.getAreas());
        req.setStoreNo(fulfillmentQueryInput.getStoreNo());
        req.setDeliveryDateBegin(fulfillmentQueryInput.getDeliveryDateBegin());

        DubboResponse<List<FulfillmentOrderResp>> response = fulfillmentOrderQueryProvider.queryWaitFulfillment(req);
        if (response == null || !response.isSuccess()){
            throw new ProviderException("调用OFC接口查询履约单数据异常");
        }
        List<FulfillmentOrderResp> data = response.getData();
        data = Optional.ofNullable(data).orElse(new ArrayList<>());
        return data.stream().map(FulfillmentOrderConverter::resp2dto).collect(Collectors.toList());

    }
}
