package net.summerfarm.wnc.facade.saas.converter;

import com.cosfo.manage.client.tenant.resp.TenantResp;
import net.summerfarm.wnc.facade.saas.dto.TenantDTO;

/**
 * Description: <br/>
 * date: 2023/4/4 17:22<br/>
 *
 * <AUTHOR> />
 */
public class TenantConverter {
    public static TenantDTO tenantResp2Dto (TenantResp tenantResp){
        if(tenantResp == null){
            return null;
        }
        TenantDTO tenantDTO = new TenantDTO();
        tenantDTO.setId(tenantResp.getId());
        tenantDTO.setPhone(tenantResp.getPhone());
        tenantDTO.setTenantName(tenantResp.getTenantName());
        tenantDTO.setAdminId(tenantResp.getAdminId());
        tenantDTO.setCompanyName(tenantResp.getCompanyName());

        return tenantDTO;
    }
}
