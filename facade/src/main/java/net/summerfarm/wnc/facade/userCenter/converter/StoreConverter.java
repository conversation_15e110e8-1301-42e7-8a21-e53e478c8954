package net.summerfarm.wnc.facade.userCenter.converter;

import net.summerfarm.wnc.facade.userCenter.dto.StoreDTO;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;

/**
 * Description:门店转换器
 * date: 2023/12/4 16:41
 *
 * <AUTHOR>
 */
public class StoreConverter {

    public static StoreDTO resp2dto(MerchantStoreResultResp merchantStoreResultResp){
        if(merchantStoreResultResp == null){
            return null;
        }
        StoreDTO storeDTO = new StoreDTO();
        storeDTO.setId(merchantStoreResultResp.getId());
        storeDTO.setMId(merchantStoreResultResp.getMId());
        storeDTO.setStoreName(merchantStoreResultResp.getStoreName());
        return storeDTO;
    }
}
