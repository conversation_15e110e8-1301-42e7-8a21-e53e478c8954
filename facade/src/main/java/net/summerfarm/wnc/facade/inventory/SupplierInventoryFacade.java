package net.summerfarm.wnc.facade.inventory;

import net.summerfarm.wnc.facade.inventory.input.CleanDefaultSupplierInventoryInput;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.saleinventory.dto.req.CleanDefaultSupplierInventoryReqDTO;
import net.xianmu.inventory.client.supplierinventory.SupplierWarehouseInventoryCommandProvider;
import net.xianmu.inventory.client.supplierinventory.dto.res.SupplierCoverOnlineResDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 供应商库存Facade
 *
 * <AUTHOR>
 */
@Component
public class SupplierInventoryFacade {

    @DubboReference(check = false)
    private SupplierWarehouseInventoryCommandProvider supplierWarehouseInventoryCommandProvider;

    /**
     * 清理默认供应商库存
     *
     * @param input
     */
    public void cleanDefaultSupplierInventory(CleanDefaultSupplierInventoryInput input) {
        if (input == null || input.getWarehouseNo() == null || StringUtils.isEmpty(input.getSkuCode()) || StringUtils.isEmpty(input.getIdempotentNo())) {
            throw new BizException("库存仓和sku和幂等单号不能为空");
        }
        CleanDefaultSupplierInventoryReqDTO reqDTO = new CleanDefaultSupplierInventoryReqDTO();
        reqDTO.setWarehouseNo(input.getWarehouseNo());
        reqDTO.setSkuCode(input.getSkuCode());
        reqDTO.setIdempotentNo(input.getIdempotentNo());
        reqDTO.setOperatorNo(input.getOperatorNo());
        reqDTO.setOperatorName(input.getOperatorName());
        DubboResponse<SupplierCoverOnlineResDTO> dubboResponse = supplierWarehouseInventoryCommandProvider.cleanDefaultSupplierInventory(reqDTO);
        if (null == dubboResponse || !dubboResponse.isSuccess()) {
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
    }

}
