package net.summerfarm.wnc.facade.userCenter;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.query.fence.StoreQuery;
import net.summerfarm.wnc.facade.userCenter.converter.AddressConverter;
import net.summerfarm.wnc.facade.userCenter.converter.StoreConverter;
import net.summerfarm.wnc.facade.userCenter.dto.AddressDTO;
import net.summerfarm.wnc.facade.userCenter.dto.StoreAddressDTO;
import net.summerfarm.wnc.facade.userCenter.dto.StoreDTO;
import net.summerfarm.wnc.facade.userCenter.input.AddressInput;
import net.summerfarm.wnc.facade.userCenter.input.MerchantInput;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.merchant.resp.domain.MerchantAddressDomainResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description:用户中心查询
 * date: 2023/12/1 15:09
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserCenterQueryFacade {

    @DubboReference
    private MerchantAddressQueryProvider merchantAddressQueryProvider;

    @DubboReference
    private MerchantStoreQueryProvider merchantStoreQueryProvider;


    public List<StoreAddressDTO> queryFenceAreaStores(AddressInput addressInput){
        List<AddressDTO> addresses = this.queryAddresses(addressInput);
        if (CollectionUtils.isEmpty(addresses)){
            return Collections.emptyList();
        }
        List<Long> storeIds = addresses.stream().map(AddressDTO::getStoreId).collect(Collectors.toList());
        List<StoreDTO> stores = this.queryStores(MerchantInput.builder().storeIds(storeIds).build());
        Map<Long/*storeId*/, String/*storeName*/> storeMap = stores.stream().collect(Collectors.toMap(StoreDTO::getId, StoreDTO::getStoreName, (oldData, newData) -> newData));
        List<StoreAddressDTO> storeAddressDTOList = addresses.stream().map(e -> {
            StoreAddressDTO storeAddressDTO = new StoreAddressDTO();
            storeAddressDTO.setMId(e.getMId());
            storeAddressDTO.setPhone(CollectionUtils.isEmpty(e.getAddressContactDTOList()) ? null : e.getAddressContactDTOList().get(0).getPhone());
            storeAddressDTO.setMName(storeMap.get(e.getStoreId()));
            return storeAddressDTO;
        }).collect(Collectors.toList());
        return storeAddressDTOList;
    }

    private List<StoreDTO> queryStores(MerchantInput merchantInput) {
        DubboResponse<List<MerchantStoreResultResp>> storeResponse = merchantStoreQueryProvider.getMerchantStoreByIds(merchantInput.getStoreIds());
        if (storeResponse == null || !storeResponse.isSuccess()){
            throw new ProviderException("调用用户中心获取门店信息异常");
        }
        return storeResponse.getData().stream().map(StoreConverter::resp2dto).collect(Collectors.toList());
    }

    public List<StoreDTO> queryMerchantStores(MerchantInput merchantInput) {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setTenantId(merchantInput.getTenantId());
        req.setPhone(merchantInput.getPhone());
        req.setMId(merchantInput.getMId());
        req.setStatus(merchantInput.getStatus());
        DubboResponse<List<MerchantStoreResultResp>> storeResponse = merchantStoreQueryProvider.getMerchantStoresByPrimaryKeys(req);
        if (storeResponse == null || !storeResponse.isSuccess()){
            throw new ProviderException("调用用户中心获取门店信息异常");
        }
        return storeResponse.getData().stream().map(StoreConverter::resp2dto).collect(Collectors.toList());
    }

    public List<AddressDTO> queryAddresses(AddressInput addressInput) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setStoreIdList(addressInput.getStoreIds());
        req.setCity(addressInput.getCity());
        req.setAreaList(addressInput.getAreas());
        req.setXmContactIdList(addressInput.getContactIds());
        req.setTenantId(addressInput.getTenantId());
        req.setStatus(addressInput.getStatus());
        req.setPageSize(1000);
        DubboResponse<List<MerchantAddressDomainResp>> contactResponse = merchantAddressQueryProvider.getAddressAndContacts(req);

        if (contactResponse == null || !contactResponse.isSuccess()){
            throw new ProviderException("调用用户中心获取地址信息异常");
        }
        return contactResponse.getData().stream().map(AddressConverter::resp2dto).collect(Collectors.toList());
    }

    public List<AddressDTO> queryAddressByQuery(StoreQuery storeQuery) {
        List<StoreDTO> stores = this.queryMerchantStores(MerchantInput.builder()
                .mId(storeQuery.getMerchantId())
                .phone(storeQuery.getPhone())
                .tenantId(AppConsts.Tenant.XM_TENANT_ID)
                .status(1).build());
        if (CollectionUtils.isEmpty(stores)){
            return Collections.emptyList();
        }
        List<Long> storeIds = stores.stream().map(StoreDTO::getId).collect(Collectors.toList());
        return this.queryAddresses(AddressInput.builder().storeIds(storeIds).tenantId(AppConsts.Tenant.XM_TENANT_ID).status(1).build());
    }
}
