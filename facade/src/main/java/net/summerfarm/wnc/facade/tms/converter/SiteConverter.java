package net.summerfarm.wnc.facade.tms.converter;

import net.summerfarm.common.util.es.dto.Poi;
import net.summerfarm.tms.client.dist.resp.DistSiteResp;
import net.summerfarm.wnc.facade.tms.dto.SiteDTO;

/**
 * Description: <br/>
 * date: 2023/10/8 14:41<br/>
 *
 * <AUTHOR> />
 */
public class SiteConverter {

    public static SiteDTO resp2DTO(DistSiteResp distSiteResp){
        if(distSiteResp == null){
            return null;
        }
        SiteDTO siteDTO = new SiteDTO();

        siteDTO.setId(distSiteResp.getId());
        siteDTO.setOutBusinessNo(distSiteResp.getOutBusinessNo());
        siteDTO.setProvince(distSiteResp.getProvince());
        siteDTO.setCity(distSiteResp.getCity());
        siteDTO.setArea(distSiteResp.getArea());
        siteDTO.setAddress(distSiteResp.getAddress());
        siteDTO.setPoi(distSiteResp.getPoi());
        siteDTO.setPhone(distSiteResp.getPhone());
        siteDTO.setName(distSiteResp.getName());
        siteDTO.setContactPerson(distSiteResp.getContactPerson());
        siteDTO.setType(distSiteResp.getType());
        siteDTO.setSuperviseSiteId(distSiteResp.getSuperviseSiteId());
        siteDTO.setDistance(distSiteResp.getDistance());
        siteDTO.setOuterClientName(distSiteResp.getOuterClientName());
        siteDTO.setFullAddress(distSiteResp.getFullAddress());
        siteDTO.setCreator(distSiteResp.getCreator());
        siteDTO.setCreatorName(distSiteResp.getCreatorName());
        siteDTO.setCreateTime(distSiteResp.getCreateTime());
        siteDTO.setIntelligencePath(distSiteResp.getIntelligencePath());
        siteDTO.setOutTime(distSiteResp.getOutTime());
        siteDTO.setState(distSiteResp.getState());
        siteDTO.setPunchDistance(distSiteResp.getPunchDistance());

        return siteDTO;
    }
}
