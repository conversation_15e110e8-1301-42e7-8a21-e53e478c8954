package net.summerfarm.wnc.facade.auth;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.admin.AdminDataPermissionQueryProvider;
import net.summerfarm.client.req.admin.AdminDataPermissionQueryReq;
import net.summerfarm.client.resp.admin.AdminDataPermissionResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.security.ProviderException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 数据权限<br/>
 * date: 2024/7/9 15:43<br/>
 *
 * <AUTHOR> />
 */
@Component
@Slf4j
public class AdminDataPermissionQueryFacade {

    @DubboReference
    private AdminDataPermissionQueryProvider adminDataPermissionQueryProvider;

    public List<Integer> queryWarehouseNosPermission(Integer adminId) {
        if(adminId == null){
            throw new ProviderException("admin不能为空");
        }
        AdminDataPermissionQueryReq req = new AdminDataPermissionQueryReq();
        req.setAdminId(adminId);
        DubboResponse<List<AdminDataPermissionResp>> resp = adminDataPermissionQueryProvider.queryAdminPermission(req);
        if(resp == null || !resp.isSuccess()){
            log.error("dubbo请求查询仓库数据权限异常,adminId为:{}，返回信息{}",adminId, JSON.toJSONString(resp));
            throw new RuntimeException("请求Auth获取仓库数据权限异常");
        }
        List<AdminDataPermissionResp> dataRespList = resp.getData();
        if(CollectionUtils.isEmpty(dataRespList)){
            return Collections.emptyList();
        }
        Optional<AdminDataPermissionResp> first = dataRespList.stream().filter(dataResp -> Objects.equals(dataResp.getPermissionValue(), "0")).findFirst();
        if(first.isPresent()){
            return Collections.singletonList(0);
        }

        return dataRespList.stream().map(AdminDataPermissionResp::getPermissionValue).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
    }
}
