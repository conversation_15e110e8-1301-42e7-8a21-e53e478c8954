package net.summerfarm.wnc.facade.saas;

import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.manage.client.tenant.req.TenantQueryReq;
import com.cosfo.manage.client.tenant.resp.TenantInfoResp;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.auth.dto.SaasTokenInfoDTO;
import net.summerfarm.wnc.facade.saas.converter.TenantConverter;
import net.summerfarm.wnc.facade.saas.dto.TenantDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SaasTenantQueryFacade {

    @DubboReference
    private TenantProvider tenantProvider;

    /**
     * 查询租户信息
     * @param tenantId 租户信息
     * @return 结果
     */
    public TenantDTO queryTenantInfo(Long tenantId){
        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setTenantId(tenantId);
        DubboResponse<List<TenantResp>> list = null;
        try {
            list = tenantProvider.list(tenantQueryReq);
        } catch (Exception e) {
            log.error("查询Saas租户信息异常",e);
            return new TenantDTO();
        }
        if(list == null || !list.isSuccess()){
            throw new BizException("获取租户信息异常");
        }
        List<TenantResp> tenantResps = list.getData();
        if(CollectionUtils.isEmpty(tenantResps)){
            return new TenantDTO();
        }
        return TenantConverter.tenantResp2Dto(tenantResps.get(0));
    }

    public SaasTokenInfoDTO getXmTmpToken(String saasToken){
        DubboResponse<TenantInfoResp> response = tenantProvider.getTenantInfo(saasToken);
        if (null == response || !response.isSuccess()){
            throw new ProviderException(response == null ? ProviderErrorCode.DEFAULT_CODE : response.getMsg());
        }
        TenantInfoResp data = response.getData();
        return SaasTokenInfoDTO.builder()
                .adminId(Optional.ofNullable(data.getAdminId()).orElse(null))
                .phone(data.getPhone())
                .tenantName(data.getName())
                .tenantId(data.getTenantId())
                .tenantAccountId(data.getTenantAccountId()).build();
    }

    /**
     * 批量查询租户信息
     * @param tenantIds 租户信息
     * @return 结果
     */
    public List<TenantDTO> queryTenantInfoList(List<Long> tenantIds){
        if(CollectionUtils.isEmpty(tenantIds)){
            return Collections.emptyList();
        }
        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setTenantIds(tenantIds);
        DubboResponse<List<TenantResp>> list = null;
        try {
            list = tenantProvider.list(tenantQueryReq);
        } catch (Exception e) {
            log.error("查询Saas租户信息异常",e);
            throw new BizException("查询Saas租户信息异常");
        }
        if(list == null || !list.isSuccess()){
            throw new BizException("获取租户信息异常");
        }
        List<TenantResp> tenantResps = list.getData();
        if(CollectionUtils.isEmpty(tenantResps)){
            return Collections.emptyList();
        }
        return tenantResps.stream().map(TenantConverter::tenantResp2Dto).collect(Collectors.toList());
    }

}