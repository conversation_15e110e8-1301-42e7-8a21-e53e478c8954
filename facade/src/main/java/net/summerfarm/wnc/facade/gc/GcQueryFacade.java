package net.summerfarm.wnc.facade.gc;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.area.AreaQueryProvider;
import net.summerfarm.goods.client.enums.AgentTypeEnum;
import net.summerfarm.goods.client.enums.SubAgentTypeEnum;
import net.summerfarm.goods.client.provider.ProductsSkuQueryProvider;
import net.summerfarm.goods.client.req.ProductSkuBasicReq;
import net.summerfarm.goods.client.req.ProductSkuPageQueryReq;
import net.summerfarm.goods.client.resp.ProductSkuBasicResp;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.facade.gc.converter.ProductSkuConverter;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDTO;
import net.summerfarm.wnc.facade.gc.dto.ProductSkuDetailDTO;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 货品中心查询
 * <AUTHOR> />
 */
@Slf4j
@Component
public class GcQueryFacade {

    @DubboReference
    private ProductsSkuQueryProvider productsSkuQueryProvider;

    /**
     * 查询sku信息 单次100条
     * @param skuList sku集合信息
     */
    public List<ProductSkuDTO> querySkuListInfo(List<String> skuList){
        if(CollectionUtils.isEmpty(skuList)){
            return Collections.emptyList();
        }
        skuList = skuList.subList(0,Math.min(skuList.size(),100));
        ProductSkuBasicReq req = new ProductSkuBasicReq();
        req.setSkuList(skuList);
        log.info("查询货品中心 productsSkuQueryProvider.selectSkuSubTypeList req:{}", JSON.toJSONString(req));
        DubboResponse<List<ProductSkuBasicResp>> response = productsSkuQueryProvider.selectSkuSubTypeList(req);
        log.info("查询货品中心 productsSkuQueryProvider.selectSkuSubTypeList resp:{}", JSON.toJSONString(response));
        if (response == null){
            throw new ProviderException("调用货品中心查询商品数据异常");
        }
        if (!response.isSuccess()){
            throw new ProviderException(response.getMsg());
        }
        if(CollectionUtils.isEmpty(response.getData())){
            return Collections.emptyList();
        }
        return response.getData().stream().map(ProductSkuConverter::productSkuBaseResp2DTO).collect(Collectors.toList());
    }


    /**
     * 根据SKU集合查询状态启用的商品信息
     * @param skuList sku集合信息
     * @return 结果
     */
    public List<ProductSkuDetailDTO> querySkuListIsUseInfo(List<String> skuList) {
        if(CollectionUtils.isEmpty(skuList)){
            return Collections.emptyList();
        }
        List<List<String>> skuPartition = Lists.partition(skuList, 200);

        List<ProductSkuDetailResp> res = Lists.newArrayList();
        for (List<String> skus : skuPartition) {
            ProductSkuPageQueryReq productSkuPageQueryReq = new ProductSkuPageQueryReq();
            productSkuPageQueryReq.setSkuList(skus);
            productSkuPageQueryReq.setTenantId(AppConsts.Tenant.XM_TENANT_ID);
            productSkuPageQueryReq.setUseFlag(1);
            productSkuPageQueryReq.setPageSize(200);
            productSkuPageQueryReq.setIsSearchXmAgent(true);

            int pageNum = NumberUtils.INTEGER_ONE;
            boolean hasNextPage;
            do {
                productSkuPageQueryReq.setPageIndex(pageNum);
                DubboResponse<PageInfo<ProductSkuDetailResp>> pageInfoDubboResponse = productsSkuQueryProvider.selectSkuPage(productSkuPageQueryReq);
                if (Objects.isNull(pageInfoDubboResponse.getData())) {
                    break;
                }
                List<ProductSkuDetailResp> list = pageInfoDubboResponse.getData().getList();
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                res.addAll(list);
                pageNum++;
                hasNextPage = pageInfoDubboResponse.getData().isHasNextPage();
            } while (hasNextPage);
        }

        return res.stream().map(ProductSkuConverter::productSkuDetail2DetailDTO).collect(Collectors.toList());
    }

    /**
     * 根据SKU集合查询状态启用的商品信息
     * @param skuList sku集合信息
     * @return 结果
     */
    public Map<String,ProductSkuDetailDTO> querySkuListIsUseInfoMap(List<String> skuList) {
        if(CollectionUtils.isEmpty(skuList)){
            return Collections.emptyMap();
        }

        return this.querySkuListIsUseInfo(skuList).stream().collect(Collectors.toMap(ProductSkuDetailDTO::getSku, productSkuDetailDTO -> productSkuDetailDTO));
    }

    /**
     * 查询SKU集合符合Saas代仓属性信息
     * @param skuList SKU集合
     * @return 结果
     */
    public List<ProductSkuDetailDTO> querySaasProxyListBySkus(List<String> skuList) {
        if(CollectionUtils.isEmpty(skuList)){
            return Collections.emptyList();
        }
        List<List<String>> skuPartition = Lists.partition(skuList, 200);
        List<ProductSkuDetailResp> res = Lists.newArrayList();

        for (List<String> skus : skuPartition) {
            ProductSkuPageQueryReq productSkuPageQueryReq = new ProductSkuPageQueryReq();
            productSkuPageQueryReq.setSkuList(skus);
            productSkuPageQueryReq.setTenantId(AppConsts.Tenant.XM_TENANT_ID);
            productSkuPageQueryReq.setUseFlag(1);
            productSkuPageQueryReq.setPageSize(200);
            productSkuPageQueryReq.setSubAgentTypeList(Collections.singletonList(SubAgentTypeEnum.PROXY_WAREHOUSE.getValue()));
            productSkuPageQueryReq.setAgentType(AgentTypeEnum.AGENT.getValue());
            int pageNum = NumberUtils.INTEGER_ONE;
            boolean hasNextPage;
            do {
                productSkuPageQueryReq.setPageIndex(pageNum);
                DubboResponse<PageInfo<ProductSkuDetailResp>> pageInfoDubboResponse = productsSkuQueryProvider.selectSkuPage(productSkuPageQueryReq);
                if (Objects.isNull(pageInfoDubboResponse.getData())) {
                    break;
                }
                List<ProductSkuDetailResp> list = pageInfoDubboResponse.getData().getList();
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                res.addAll(list);
                pageNum++;
                hasNextPage = pageInfoDubboResponse.getData().isHasNextPage();
            } while (hasNextPage);
        }

        return res.stream().map(ProductSkuConverter::productSkuDetail2DetailDTO).collect(Collectors.toList());
    }

    /**
     * 查询Saas自营代销的SKU
     * @param skuList SKU集合
     * @return 结果
     */
    public List<String> querySaasProxySkuListBySkus(List<String> skuList){
        List<ProductSkuDetailDTO> productSkuDetailDTOS = this.querySaasProxyListBySkus(skuList);
        if(CollectionUtils.isEmpty(productSkuDetailDTOS)){
            return Collections.emptyList();
        }

        return productSkuDetailDTOS.stream().map(ProductSkuDetailDTO::getSku).collect(Collectors.toList());
    }
}
