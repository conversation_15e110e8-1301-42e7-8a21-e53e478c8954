package net.summerfarm.wnc.facade.wms;

import net.summerfarm.wms.storage.WarehouseNameRefreshProvider;
import net.summerfarm.wms.storage.dto.WarehouseNameRefreshDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
public class WarehouseNameRefreshFacade {

    @DubboReference
    private WarehouseNameRefreshProvider warehouseNameRefreshProvider;

    public void refresh(Integer warehouseNo, String warehouseName){
        warehouseNameRefreshProvider.refreshWarehouseName(WarehouseNameRefreshDTO.builder()
                .warehouseName(warehouseName)
                .warehouseNo(warehouseNo.longValue())
                .build());
    }
}
