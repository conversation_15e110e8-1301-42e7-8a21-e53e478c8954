package net.summerfarm.wnc.facade.gc;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.enums.CrossBizGoodsMappingEnum;
import net.summerfarm.goods.client.provider.CrossBizGoodsMappingQueryProvider;
import net.summerfarm.goods.client.req.CrossBizGoodsMappingQueryReq;
import net.summerfarm.goods.client.resp.CrossBizGoodsMappingResp;
import net.summerfarm.wnc.facade.gc.dto.GoodsSkuMappingDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 货品映射关系
 * date: 2025/4/29 10:47<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class CrossBizGoodsMappingQueryFacade {

    @DubboReference
    private CrossBizGoodsMappingQueryProvider crossBizGoodsMappingQueryProvider;
    @Resource
    private GcQueryFacade gcQueryFacade;

    public List<GoodsSkuMappingDTO> queryMappingConfig(List<String> skus) {
        if(CollectionUtils.isEmpty(skus)){
            return Collections.emptyList();
        }
        if(CollectionUtils.isEmpty(skus.stream().filter(Objects::nonNull).collect(Collectors.toList()))){
            return Collections.emptyList();
        }
        CrossBizGoodsMappingQueryReq req = new CrossBizGoodsMappingQueryReq();
        req.setBizType(CrossBizGoodsMappingEnum.BizType.XIANMU_TO_POP.getValue());
        req.setTargetSkuList(skus);
        DubboResponse<List<CrossBizGoodsMappingResp>> dubboResponse = crossBizGoodsMappingQueryProvider.queryMappingConfig(req);
        if (dubboResponse == null || !dubboResponse.isSuccess()) {
            log.error("\n crossBizGoodsMappingQueryProvider queryMappingConfig error \n");
            return Collections.emptyList();
        }

        List<CrossBizGoodsMappingResp> dataList = dubboResponse.getData();
        if(CollectionUtils.isEmpty(dataList)){
            return Collections.emptyList();
        }

        List<GoodsSkuMappingDTO> goodsSkuMappingDTOList = dataList.stream().map(e -> {
            GoodsSkuMappingDTO goodsSkuMappingDTO = new GoodsSkuMappingDTO();
            goodsSkuMappingDTO.setXmSku(e.getSrcSku());
            goodsSkuMappingDTO.setPopSku(e.getTargetSku());

            return goodsSkuMappingDTO;
        }).collect(Collectors.toList());

        return goodsSkuMappingDTOList;
    }


    public Map<String,String> queryPopToXianMuMapping(List<String> skus){
        if (CollectionUtils.isEmpty(skus)) {
            return Collections.emptyMap();
        }

        List<GoodsSkuMappingDTO> goodsSkuMappingDTOS = this.queryMappingConfig(skus);

        if (CollectionUtils.isEmpty(goodsSkuMappingDTOS)) {
            return Collections.emptyMap();
        }
        Map<String, String> pop2xmSkuMap = goodsSkuMappingDTOS.stream()
                .filter(goodsSkuMappingDTO -> goodsSkuMappingDTO.getPopSku() != null)
                .collect(Collectors.toMap(GoodsSkuMappingDTO::getPopSku, GoodsSkuMappingDTO::getXmSku, (n, o) -> n));

        return pop2xmSkuMap;
    }
}
