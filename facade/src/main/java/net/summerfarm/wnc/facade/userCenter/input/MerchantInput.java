package net.summerfarm.wnc.facade.userCenter.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description:门店查询
 * date: 2023/12/1 15:19
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantInput {

    /**
     * 鲜沐门店ID
     */
    private Long mId;

    /**
     * 门店注册手机号
     */
    private String phone;

    /**
     * 门店ID集合
     */
    private List<Long> storeIds;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店状态
     */
    private Integer status;
}
