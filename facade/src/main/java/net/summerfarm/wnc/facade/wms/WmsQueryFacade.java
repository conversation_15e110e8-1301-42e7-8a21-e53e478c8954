package net.summerfarm.wnc.facade.wms;

import net.summerfarm.wms.config.WarehouseConfigProvider;
import net.summerfarm.wms.config.dto.WarehouseConfigCabinetStatusDTO;
import net.summerfarm.wms.config.resp.WarehouseConfigCabinetStatusResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: WMS查询<br/>
 * date: 2024/4/18 14:41<br/>
 *
 * <AUTHOR> />
 */
@Component
public class WmsQueryFacade {

    @DubboReference
    private WarehouseConfigProvider warehouseConfigProvider;

    public Map<Integer,String> queryAllCabinetStatus(){
        DubboResponse<WarehouseConfigCabinetStatusResp> resp = warehouseConfigProvider.queryAllCabinetStatus();
        if (resp == null || !resp.isSuccess()){
            throw new ProviderException("调用WMS获取仓库库位精细化异常");
        }

        WarehouseConfigCabinetStatusResp data = resp.getData();
        if(data == null){
            return Collections.emptyMap();
        }
        List<WarehouseConfigCabinetStatusDTO> cabinetStatusDTOList = data.getCabinetStatusDTOList();
        if(CollectionUtils.isEmpty(cabinetStatusDTOList)){
            return Collections.emptyMap();
        }
        return cabinetStatusDTOList.stream()
                .collect(Collectors.toMap(WarehouseConfigCabinetStatusDTO::getWarehouseNo, WarehouseConfigCabinetStatusDTO::getCabinetStatus));
    }

    public Map<Integer, WarehouseConfigCabinetStatusDTO> queryAllWarehouseCabinetStatus() {
        DubboResponse<WarehouseConfigCabinetStatusResp> resp = warehouseConfigProvider.queryAllCabinetStatus();
        if (resp == null || !resp.isSuccess()) {
            throw new ProviderException("调用WMS获取仓库库位精细化异常");
        }

        WarehouseConfigCabinetStatusResp data = resp.getData();
        if (data == null) {
            return Collections.emptyMap();
        }
        List<WarehouseConfigCabinetStatusDTO> cabinetStatusDTOList = data.getCabinetStatusDTOList();
        if (CollectionUtils.isEmpty(cabinetStatusDTOList)) {
            return Collections.emptyMap();
        }
        return cabinetStatusDTOList.stream()
                .collect(Collectors.toMap(WarehouseConfigCabinetStatusDTO::getWarehouseNo, Function.identity(), (a, b) -> a));
    }

}
