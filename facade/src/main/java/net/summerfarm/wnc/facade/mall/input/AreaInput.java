package net.summerfarm.wnc.facade.mall.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description:运营服务区域查询
 * date: 2023/12/8 19:20
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AreaInput {

    /**
     * 运营服务区域编号集合
     */
    private List<Integer> areaNos;
}
