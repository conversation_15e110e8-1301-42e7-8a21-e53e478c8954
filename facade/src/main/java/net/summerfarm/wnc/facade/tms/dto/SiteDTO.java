package net.summerfarm.wnc.facade.tms.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: 点位信息<br/>
 * date: 2023/10/8 14:39<br/>
 *
 * <AUTHOR> />
 */
@Data
public class SiteDTO {
    /**
     * 点位ID
     */
    private Long id;
    /**
     * 业务编号
     */
    private String outBusinessNo;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String area;
    /**
     * 地址
     */
    private String address;
    /**
     * poi
     */
    private String poi;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 名称
     */
    private String name;
    /**
     * 联系人名称
     */
    private String contactPerson;
    /**
     * 点位类型
     *
     * @see TmsSiteTypeEnum
     */
    private Integer type;
    /**
     * 监管仓对应的库存仓
     */
    private Long superviseSiteId;

    /**
     * 到仓距离
     */
    private BigDecimal distance;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 完整地址（包含省市区)
     */
    private String fullAddress;

    /**
     *  创建人id
     */
    private Long creator;

    /**
     *  创建人名称
     */
    private String creatorName;

    /**
     *  创建时间
     */
    private LocalDateTime createTime;

    /**
     * 0智能排线 1手动排线
     */
    private Integer intelligencePath;

    /**
     * 出仓时间
     */
    private String outTime;

    /**
     * 是否需要打卡0 不需要 1需要
     */
    private Integer state;

    /**
     * 打卡距离
     */
    private BigDecimal punchDistance;
}
