package net.summerfarm.wnc.facade.gc.dto;

import lombok.Data;
import net.summerfarm.goods.client.resp.ProductsMappingResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2024/10/24 11:50<br/>
 *
 * <AUTHOR> />
 */
@Data
public class ProductSkuDetailDTO {
    private static final long serialVersionUID = 1L;
    /**
     * skuId(saas)
     */
    private Long skuId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * spu id(saas)
     */
    private Long spuId;
    /**
     * sku编码
     */
    private String sku;
    /**
     * 只作为鲜沐供应商货品判断 0自营 1代仓
     */
    private Integer agentType;
    /** 二级类型：1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销 **/
    private Integer subAgentType;
    /**
     * 地点类型0进口1国产
     */
    private Integer placeType;
    /**
     * 体积单位
     */
    private Long volumeUnit;
    /**
     * 体积
     */
    private String volume;
    /**
     * 规格
     */
    private String specification;
    /**
     * 是否关联商品只适用于代仓品
     */
    private Integer associated;
    /**
     * 重量备注
     */
    private String weightNotes;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /** 规格类型，0-容量*数量，1-区间 **/
    private Integer specificationType;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 税率(目前saas侧使用)
     */
    private BigDecimal taxRateValue;
    /**
     * 自有编码
     */
    private String customSkuCode;
    /**
     * 停用状态 0停用1启用
     */
    private Integer useFlag;
    /**
     * 上新类型：0、平台 1、大客户 2、saas自营&代仓 3、仅saas自营
     */
    private Integer createType;
    /**
     * sku图片
     */
    private String skuPicture;
    /**
     * sku实物名称
     */
    private String skuTitle;
    /**
     * 所属客户ID（admin#id）
     */
    private Integer ownerId;
    /**
     * 一级类目
     **/
    private String firstCategory;
    /** 一级类目ID **/
    private Long firstCategoryId;
    /**
     * 二级类目
     **/
    private String secondCategory;
    /** 二级类目ID **/
    private Long secondCategoryId;
    /**
     * 类目id
     */
    private Long categoryId;
    /** 类目名称 **/
    private String categoryName;

    /** 所属类目类型 **/
    private Integer categoryType;
    /**
     * 主标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;
    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     * 注意：鲜沐温区枚举和货品中心枚举值不同
     * {@link  net.summerfarm.goods.client.enums.GoodsStorageLocationEnum }
     */
    private Integer storageLocation;
    /**
     * 存储温度
     */
    private String storageTemperature;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;
    /**
     * 产地
     */
    private String origin;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * spu自由编码
     */
    private String customSpuCode;

    /** 映射关系 **/
    private ProductsMappingResp skuMapping;
    /**
     * 基础规格单位
     */
    private String basicSpecUnit;

    /** sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋 **/
    private Integer extType;

    /**
     * 物料类型
     */
    private Integer materialType;

    /**
     * 买手ID
     */
    private Long buyerId;

    /**
     * 买手名称
     */
    private String buyerName;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;

    /**
     * 净重量单位
     */
    private String netWeightUnit;
}
