package net.summerfarm.wnc.facade.userCenter.converter;

import net.summerfarm.wnc.facade.userCenter.dto.AddressDTO;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import net.xianmu.usercenter.client.merchant.resp.domain.MerchantAddressDomainResp;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:地址转换器
 * date: 2023/12/4 16:41
 *
 * <AUTHOR>
 */
public class AddressConverter {

    public static AddressDTO resp2dto(MerchantAddressDomainResp merchantAddressDomainResp){
        if(merchantAddressDomainResp == null ){
            return null;
        }
        AddressDTO addressDTO = new AddressDTO();
        addressDTO.setStoreId(merchantAddressDomainResp.getStoreId());
        addressDTO.setMId(merchantAddressDomainResp.getMId());
        addressDTO.setProvince(merchantAddressDomainResp.getProvince());
        addressDTO.setCity(merchantAddressDomainResp.getCity());
        addressDTO.setArea(merchantAddressDomainResp.getArea());
        addressDTO.setAddress(merchantAddressDomainResp.getAddress());
        List<MerchantContactResultResp> contactList = merchantAddressDomainResp.getContactList();
        if (!CollectionUtils.isEmpty(contactList)){
            addressDTO.setAddressContactDTOList(contactList.stream().map(AddressContactConverter::resp2dto).collect(Collectors.toList()));
        }
        return addressDTO;
    }
}
