package net.summerfarm.wnc.facade.mall.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:运营服务区数据转换对象
 * date: 2023/12/8 19:18
 *
 * <AUTHOR>
 */
@Data
public class AreaDTO implements Serializable {

    private static final long serialVersionUID = -5331567640269036940L;

    /**
     * 运营区域编号
     */
    private Integer areaNo;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * 运营区域状态, 0: 停用, 1: 启用
     */
    private Integer areaStatus;

    /**
     * 大区编号
     */
    private Integer largeAreaNo;

    /**
     * 大区名称
     */
    private String largeAreaName;

    /**
     * 大区状态,  0: 停用, 1: 启用
     */
    private Integer largeAreaStatus;
}
