package net.summerfarm.wnc.facade.saas;

import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.manage.client.tenant.resp.TenantInfoResp;
import net.summerfarm.wnc.common.auth.dto.SaasTokenInfoDTO;
import net.summerfarm.wnc.common.enums.WarehouseSourceEnum;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;


/**
 * Description:XmAuthFacade
 * date: 2023/4/7 16:25
 *
 * <AUTHOR>
 */
@Component
public class XmAuthFacade {

    @DubboReference
    private TenantProvider tenantProvider;

    public SaasTokenInfoDTO doRpcCheckSaasToken(String saasToken) {
        DubboResponse<TenantInfoResp> response = tenantProvider.getTenantInfo(saasToken);
        TenantInfoResp data = response.getData();
        if (data == null) {
            return SaasTokenInfoDTO.builder()
                    .tenantId(Long.parseLong(WarehouseSourceEnum.SAAS_WAREHOUSE.getCode().toString())).build();
        }
        return SaasTokenInfoDTO.builder()
                .adminId(data.getAdminId())
                .phone(data.getPhone())
                .tenantName(data.getName())
                .tenantId(data.getTenantId())
                .tenantAccountId(data.getTenantAccountId())
                .tenantAccountName(data.getTenantAccountName())
                .build();
    }
}
