package net.summerfarm.wnc.facade.ofc.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * Description:履约单变更输入参数
 * date: 2023/8/31 19:01
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FulfillmentChangeCommandInput implements Serializable {

    private static final long serialVersionUID = -1107180809328585845L;

    /**
     * 外部单号
     */
    private String outerOrderId;

    /**
     * 来源，200：鲜沐-订单，201：鲜沐-售后，202：鲜沐-样品，203：鲜沐-省心送，210：saas-订单，211：saas-售后
     */
    private Integer source;

    /**
     * 外部联系人ID
     */
    private String outerContactId;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 原城配仓编号
     */
    private Integer oldStoreNo;

    /**
     * 新城配仓编号
     */
    private Integer newStoreNo;

    /**
     * 创建人
     */
    private String creator;
    /**
     * 围栏id
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 区域id
     */
    private Integer adCodeMsgId;

    /**
     * 自定义区域名称
     */
    private String customAreaName;
}
