package net.summerfarm.wnc.infrastructure.repository;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.client.req.WarehouseStorageListQueryReq;
import net.summerfarm.wnc.common.enums.WarehouseStorageCenterEnums;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageQuery;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageCenterWorkEntity;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.summerfarm.wnc.infrastructure.converter.*;
import net.summerfarm.wnc.infrastructure.mapper.*;
import net.summerfarm.wnc.infrastructure.model.*;
import net.summerfarm.wnc.infrastructure.util.PageInfoHelper;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/3/28 17:35<br/>
 *
 * <AUTHOR> />
 */
@Service
public class WarehouseStorageCenterRepositoryImpl implements WarehouseStorageCenterRepository {

    @Resource
    private WarehouseStorageCenterMapper warehouseStorageCenterMapper;
    @Resource
    private ConfigRepository configRepository;
    @Resource
    private WncWarehouseStorageFenceMapper wncWarehouseStorageFenceMapper;
    @Resource
    private WarehouseStorageCenterBusinessMapper warehouseStorageCenterBusinessMapper;
    @Resource
    private WarehouseTakeStandardMapper warehouseTakeStandardMapper;
    @Resource
    private WncWarehouseStorageTenantMapper wncWarehouseStorageTenantMapper;
    @Resource
    private WarehouseStorageCenterBusinessWorkMapper warehouseStorageCenterBusinessWorkMapper;
    @Resource
    private WarehouseLogisticsMappingMapper warehouseLogisticsMappingMapper;

    @Override
    public void save(WarehouseStorageEntity warehouseStorageEntity) {
        //查询数量作为库存仓编号
        WarehouseStorageCenter warehouseStorageCenter = warehouseStorageCenterMapper.selectOne(new LambdaQueryWrapper<WarehouseStorageCenter>()
                .orderByDesc(WarehouseStorageCenter::getWarehouseNo).last("limit 1")
        );
        warehouseStorageEntity.setWarehouseNo(warehouseStorageCenter.getWarehouseNo() + 1);
        WarehouseStorageCenter warehouseStorageCenterSave = WarehouseStorageCenterConverter.entity2WarehouseStorage(warehouseStorageEntity);
        warehouseStorageCenterMapper.insert(warehouseStorageCenterSave);
        warehouseStorageEntity.setId(warehouseStorageCenterSave.getId());
        if(warehouseStorageEntity.getWarehouseStorageCenterBusEntity() != null){
            WarehouseStorageCenterBusiness warehouseStorageCenterBusiness = WarehouseStorageCenterBusinessConverter.entity2Model(warehouseStorageEntity.getWarehouseStorageCenterBusEntity());
            warehouseStorageCenterBusiness.setCapacity(warehouseStorageCenterBusiness.getCapacity() * 1000);
            warehouseStorageCenterBusiness.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
            warehouseStorageCenterBusinessMapper.insert(warehouseStorageCenterBusiness);
        }
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseTakeStandardEntities())){
            List<WarehouseTakeStandard> warehouseTakeStandards = warehouseStorageEntity.getWarehouseTakeStandardEntities().stream()
                    .map(WarehouseTakeStandardConverter::entity2model).collect(Collectors.toList());
            for (WarehouseTakeStandard warehouseTakeStandard : warehouseTakeStandards) {
                warehouseTakeStandard.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
                warehouseTakeStandard.setCreateAdminId(warehouseStorageEntity.getCreator());
                warehouseTakeStandardMapper.insert(warehouseTakeStandard);
            }
        }
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseStorageFenceEntities())){
            List<WncWarehouseStorageFence> rcWarehouseStorageFences = warehouseStorageEntity.getWarehouseStorageFenceEntities().stream()
                    .map(WncWarehouseStorageFenceConverter::entity2RcWarehouseStorageFence).collect(Collectors.toList());
            for (WncWarehouseStorageFence rcWarehouseStorageFence : rcWarehouseStorageFences) {
                rcWarehouseStorageFence.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
                wncWarehouseStorageFenceMapper.insert(rcWarehouseStorageFence);
            }
        }else{
            WncWarehouseStorageFence rcWarehouseStorageFence = new WncWarehouseStorageFence();
            rcWarehouseStorageFence.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
            rcWarehouseStorageFence.setTenantId(warehouseStorageEntity.getTenantId());
            rcWarehouseStorageFence.setLastOperatorName(warehouseStorageEntity.getOperatorName());
            wncWarehouseStorageFenceMapper.insert(rcWarehouseStorageFence);
        }
    }

    @Override
    public void update(WarehouseStorageEntity warehouseStorageEntity) {
        WarehouseStorageCenter warehouseStorageCenter = warehouseStorageCenterMapper.selectById(warehouseStorageEntity.getId());
        warehouseStorageCenterMapper.updateById(WarehouseStorageCenterConverter.entity2WarehouseStorage(warehouseStorageEntity));

        if(warehouseStorageEntity.getWarehouseStorageCenterBusEntity() != null){
            //删除老的
            warehouseStorageCenterBusinessMapper.delete(new LambdaQueryWrapper<WarehouseStorageCenterBusiness>()
                    .eq(WarehouseStorageCenterBusiness::getWarehouseNo, warehouseStorageCenter.getWarehouseNo()));
            WarehouseStorageCenterBusiness warehouseStorageCenterBusiness = WarehouseStorageCenterBusinessConverter.entity2Model(warehouseStorageEntity.getWarehouseStorageCenterBusEntity());
            warehouseStorageCenterBusiness.setWarehouseNo(warehouseStorageCenter.getWarehouseNo());
            //新增新的
            warehouseStorageCenterBusinessMapper.insert(warehouseStorageCenterBusiness);
        }
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseTakeStandardEntities())){
            //删除新增
            warehouseTakeStandardMapper.delete(new LambdaQueryWrapper<WarehouseTakeStandard>()
                    .eq(WarehouseTakeStandard::getWarehouseNo,warehouseStorageCenter.getWarehouseNo()));

            List<WarehouseTakeStandard> warehouseTakeStandards = warehouseStorageEntity.getWarehouseTakeStandardEntities().stream().map(WarehouseTakeStandardConverter::entity2model).collect(Collectors.toList());
            for (WarehouseTakeStandard warehouseTakeStandard : warehouseTakeStandards) {
                warehouseTakeStandard.setWarehouseNo(warehouseStorageCenter.getWarehouseNo());
                warehouseTakeStandard.setCreateAdminId(warehouseStorageEntity.getCreator());
                warehouseTakeStandardMapper.insert(warehouseTakeStandard);
            }
        }
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseStorageFenceEntities())){
            //删除
            wncWarehouseStorageFenceMapper.delete(new LambdaQueryWrapper<WncWarehouseStorageFence>()
                    .eq(WncWarehouseStorageFence::getWarehouseNo,warehouseStorageCenter.getWarehouseNo()));
            List<WncWarehouseStorageFence> rcWarehouseStorageFences = warehouseStorageEntity.getWarehouseStorageFenceEntities().stream()
                    .map(WncWarehouseStorageFenceConverter::entity2RcWarehouseStorageFence).collect(Collectors.toList());

            for (WncWarehouseStorageFence rcWarehouseStorageFence : rcWarehouseStorageFences) {
                rcWarehouseStorageFence.setWarehouseNo(warehouseStorageCenter.getWarehouseNo());
                rcWarehouseStorageFence.setTenantId(warehouseStorageCenter.getTenantId());
                wncWarehouseStorageFenceMapper.insert(rcWarehouseStorageFence);
            }
        }
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseStorageCenterWorkEntities())){
            List<WarehouseStorageCenterBusiness> businesses = warehouseStorageCenterBusinessMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenterBusiness>()
                    .eq(WarehouseStorageCenterBusiness::getWarehouseNo, warehouseStorageCenter.getWarehouseNo())
            );
            if(!CollectionUtils.isEmpty(businesses)){
                //删除新增
                warehouseStorageCenterBusinessWorkMapper.delete(new LambdaQueryWrapper<WarehouseStorageCenterBusinessWork>()
                        .eq(WarehouseStorageCenterBusinessWork::getWarehouseNo,warehouseStorageCenter.getWarehouseNo()));

                List<WarehouseStorageCenterBusinessWork> works = warehouseStorageEntity.getWarehouseStorageCenterWorkEntities().stream()
                        .map(WarehouseStorageCenterBusinessWorkConverter::entity2Model).collect(Collectors.toList());
                for (WarehouseStorageCenterBusinessWork work : works) {
                    work.setWarehouseNo(warehouseStorageCenter.getWarehouseNo());
                    work.setWarehouseStorageCenterBusinessId(businesses.get(0).getId());
                    warehouseStorageCenterBusinessWorkMapper.insert(work);
                }
            }
        }
    }

    @Override
    public List<WarehouseStorageEntity> queryWarehouseStorage(WarehouseStorageQuery warehouseStorageQuery) {
        List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                .eq(warehouseStorageQuery.getTenantId() != null, WarehouseStorageCenter::getTenantId, warehouseStorageQuery.getTenantId())
                .eq(StringUtils.isNotBlank(warehouseStorageQuery.getWarehouseName()), WarehouseStorageCenter::getWarehouseName, warehouseStorageQuery.getWarehouseName())
                .in(!CollectionUtils.isEmpty(warehouseStorageQuery.getWarehouseNos()), WarehouseStorageCenter::getWarehouseNo, warehouseStorageQuery.getWarehouseNos())
        );
        return warehouseStorageCenters.stream().map(WarehouseStorageCenterConverter::warehouseStorage2Entity).collect(Collectors.toList());
    }

    @Override
    public WarehouseStorageEntity queryDetail(Integer id) {
        WarehouseStorageCenter warehouseStorageCenter = warehouseStorageCenterMapper.selectById(id);
        if(warehouseStorageCenter == null){
            throw new BizException("数据不存在");
        }
        WarehouseStorageCenterBusiness warehouseStorageCenterBusiness = warehouseStorageCenterBusinessMapper.selectOne(new LambdaQueryWrapper<WarehouseStorageCenterBusiness>()
                .eq(WarehouseStorageCenterBusiness::getWarehouseNo, warehouseStorageCenter.getWarehouseNo())
                .last("limit 1")
        );

        List<WarehouseTakeStandard> warehouseTakeStandards = warehouseTakeStandardMapper.selectList(new LambdaQueryWrapper<WarehouseTakeStandard>()
                .eq(WarehouseTakeStandard::getWarehouseNo, warehouseStorageCenter.getWarehouseNo())
        );

        List<WncWarehouseStorageFence> wncWarehouseStorageFences = wncWarehouseStorageFenceMapper.selectList(new LambdaQueryWrapper<WncWarehouseStorageFence>()
                .eq(WncWarehouseStorageFence::getWarehouseNo, warehouseStorageCenter.getWarehouseNo())
        );

        List<WarehouseStorageCenterBusinessWork> warehouseStorageCenterBusinessWorks = warehouseStorageCenterBusinessWorkMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenterBusinessWork>()
                .eq(WarehouseStorageCenterBusinessWork::getWarehouseNo, warehouseStorageCenter.getWarehouseNo())
                .eq(WarehouseStorageCenterBusinessWork::getIsDelete, 0));

        WarehouseStorageEntity warehouseStorageEntity = WarehouseStorageCenterConverter.warehouseStorage2Entity(warehouseStorageCenter);
        warehouseStorageEntity.setWarehouseStorageCenterBusEntity(WarehouseStorageCenterBusinessConverter.warehouseStorageCenterBus2Entity(warehouseStorageCenterBusiness));
        warehouseStorageEntity.setWarehouseTakeStandardEntities(warehouseTakeStandards.stream().map(WarehouseTakeStandardConverter::model2Entity).collect(Collectors.toList()));
        warehouseStorageEntity.setWarehouseStorageFenceEntities(wncWarehouseStorageFences.stream().map(WncWarehouseStorageFenceConverter::fence2Entity).collect(Collectors.toList()));
        warehouseStorageEntity.setWarehouseStorageCenterWorkEntities(warehouseStorageCenterBusinessWorks.stream().map(WarehouseStorageCenterBusinessWorkConverter::model2Entity).collect(Collectors.toList()));

        return warehouseStorageEntity;
    }

    @Override
    public PageInfo<WarehouseStorageEntity> querySelfWarehouseStoragePage(WarehouseStorageQuery warehouseStorageQuery) {
        List<WarehouseStorageEntity> warehouseStorageEntities = new ArrayList<>();
        PageInfo<WarehouseStorageEntity> resultPageInfo = new PageInfo();

        if(StringUtils.isBlank(warehouseStorageQuery.getWarehouseName()) && CollectionUtils.isEmpty(warehouseStorageQuery.getProvinces())
                && CollectionUtils.isEmpty(warehouseStorageQuery.getCitys()) &&  CollectionUtils.isEmpty(warehouseStorageQuery.getAreas())){
            PageHelper.startPage(warehouseStorageQuery.getPageIndex(), warehouseStorageQuery.getPageSize());
            List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                    .eq(WarehouseStorageCenter::getTenantId, warehouseStorageQuery.getTenantId())
                    .orderByDesc(WarehouseStorageCenter::getCreateTime));
            if(CollectionUtils.isEmpty(warehouseStorageCenters)){
                return new PageInfo<WarehouseStorageEntity>();
            }
            PageInfo<WarehouseStorageCenter> pageInfo = PageInfoHelper.createPageInfo(warehouseStorageCenters);

            warehouseStorageQuery.setWarehouseNos(warehouseStorageCenters.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList()));
            //查询
            warehouseStorageEntities = querySelfWarehouseStorage(warehouseStorageQuery);

            BeanUtil.copyProperties(pageInfo, resultPageInfo);
            resultPageInfo.setList(warehouseStorageEntities);
        }else if(StringUtils.isNotBlank(warehouseStorageQuery.getWarehouseName())){
            PageHelper.startPage(warehouseStorageQuery.getPageIndex(), warehouseStorageQuery.getPageSize());
            List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                    .eq(WarehouseStorageCenter::getTenantId, warehouseStorageQuery.getTenantId())
                    .like(WarehouseStorageCenter::getWarehouseName, warehouseStorageQuery.getWarehouseName())
                    .orderByDesc(WarehouseStorageCenter::getCreateTime)
            );
            if(CollectionUtils.isEmpty(warehouseStorageCenters)){
                return new PageInfo<WarehouseStorageEntity>();
            }
            PageInfo<WarehouseStorageCenter> pageInfo = PageInfoHelper.createPageInfo(warehouseStorageCenters);

            warehouseStorageQuery.setWarehouseNos(warehouseStorageCenters.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList()));
            //查询围栏信息
            warehouseStorageEntities = querySelfWarehouseStorage(warehouseStorageQuery);
            warehouseStorageQuery.setWarehouseNos(warehouseStorageEntities.stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList()));

            BeanUtil.copyProperties(pageInfo, resultPageInfo);
            resultPageInfo.setList(warehouseStorageEntities);
        }else if(!CollectionUtils.isEmpty(warehouseStorageQuery.getProvinces() ) || !CollectionUtils.isEmpty(warehouseStorageQuery.getCitys())
                || !CollectionUtils.isEmpty(warehouseStorageQuery.getAreas())){
            //需要查询出省市区对应的仓库信息，然后在查询对应的仓库
            warehouseStorageEntities = warehouseStorageCenterMapper.querySelfWarehouseStorage(warehouseStorageQuery);
            if(CollectionUtils.isEmpty(warehouseStorageEntities)){
                return new PageInfo<WarehouseStorageEntity>();
            }
            PageHelper.startPage(warehouseStorageQuery.getPageIndex(), warehouseStorageQuery.getPageSize());
            List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                    .eq(WarehouseStorageCenter::getTenantId, warehouseStorageQuery.getTenantId())
                    .in(WarehouseStorageCenter::getWarehouseNo, warehouseStorageEntities.stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList()))
                    .orderByDesc(WarehouseStorageCenter::getCreateTime)
            );
            PageInfo<WarehouseStorageCenter> pageInfo = PageInfoHelper.createPageInfo(warehouseStorageCenters);

            //查询围栏信息
            warehouseStorageQuery.setWarehouseNos(warehouseStorageCenters.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList()));
            warehouseStorageEntities = querySelfWarehouseStorage(warehouseStorageQuery);

            BeanUtil.copyProperties(pageInfo, resultPageInfo);
            resultPageInfo.setList(warehouseStorageEntities);
        }
        return resultPageInfo;
    }

    private List<WarehouseStorageEntity> querySelfWarehouseStorage(WarehouseStorageQuery warehouseStorageQuery) {
        List<String> queryNoAreaCityList = new ArrayList<>();
        //过滤没有区域的城市
        List<String> noAreaCityList = configRepository.queryNoAreaCity();
        List<String> citys = warehouseStorageQuery.getCitys();
        List<String> newHaveAreaCitys = new ArrayList<>();

        if(!CollectionUtils.isEmpty(citys)){
            citys.forEach(city ->{
                if(noAreaCityList.contains(city)){
                    queryNoAreaCityList.add(city);
                }else{
                    newHaveAreaCitys.add(city);
                }
            });
        }
        List<WarehouseStorageEntity> warehouseStorageEntities = new ArrayList<>();
        //相等说明只查询没有区域的城市
        if(queryNoAreaCityList.size() == 0){
            //只查询只有城市和区域的
            warehouseStorageQuery.setCitys(newHaveAreaCitys);
            List<WarehouseStorageEntity> warehouseStorageList = warehouseStorageCenterMapper.querySelfWarehouseStorage(warehouseStorageQuery);
            warehouseStorageEntities.addAll(warehouseStorageList);
        }else{
            //只查询只有城市的
            warehouseStorageQuery.setCitys(queryNoAreaCityList);
            warehouseStorageQuery.setAreas(null);
            List<WarehouseStorageEntity> warehouseStorageList = warehouseStorageCenterMapper.querySelfWarehouseStorage(warehouseStorageQuery);
            warehouseStorageEntities.addAll(warehouseStorageList);
        }
        return warehouseStorageEntities;
    }


    @Override
    public WarehouseStorageEntity queryByWarehouseNo(Integer warehouseNo) {
        WarehouseStorageCenter warehouseStorageCenter = warehouseStorageCenterMapper.selectOne(new LambdaQueryWrapper<WarehouseStorageCenter>()
                .eq(WarehouseStorageCenter::getWarehouseNo, warehouseNo)
                .last("limit 1")
        );
        if(warehouseStorageCenter == null){
            throw new BizException("不存在此仓库编号信息");
        }
        return WarehouseStorageCenterConverter.warehouseStorage2Entity(warehouseStorageCenter);
    }

    @Override
    public List<WarehouseStorageEntity> queryList(WarehouseStorageQuery warehouseStorageQuery) {
        List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                .eq(warehouseStorageQuery.getTenantId() != null, WarehouseStorageCenter::getTenantId, warehouseStorageQuery.getTenantId())
                .eq(warehouseStorageQuery.getStatus() != null, WarehouseStorageCenter::getStatus, warehouseStorageQuery.getStatus())
                .eq(warehouseStorageQuery.getWarehouseNo() != null, WarehouseStorageCenter::getWarehouseNo, warehouseStorageQuery.getWarehouseNo())
                .like(StringUtils.isNotBlank(warehouseStorageQuery.getWarehouseName()), WarehouseStorageCenter::getWarehouseName, warehouseStorageQuery.getWarehouseName())
                .eq(warehouseStorageQuery.getType() != null, WarehouseStorageCenter::getType, warehouseStorageQuery.getType())
                .in(!CollectionUtils.isEmpty(warehouseStorageQuery.getWarehouseNos()), WarehouseStorageCenter::getWarehouseNo, warehouseStorageQuery.getWarehouseNos())
                .notIn(!CollectionUtils.isEmpty(warehouseStorageQuery.getNoInSelfWarehouseNos()), WarehouseStorageCenter::getWarehouseNo, warehouseStorageQuery.getNoInSelfWarehouseNos())
                .in(!CollectionUtils.isEmpty(warehouseStorageQuery.getSelfWarehouseNos()), WarehouseStorageCenter::getWarehouseNo, warehouseStorageQuery.getSelfWarehouseNos())
                .like(StringUtils.isNotBlank(warehouseStorageQuery.getWarehouseLike()), WarehouseStorageCenter::getWarehouseName, warehouseStorageQuery.getWarehouseLike())
                .eq(StringUtils.isNotBlank(warehouseStorageQuery.getWarehouseNameEqualTo()), WarehouseStorageCenter::getWarehouseName, warehouseStorageQuery.getWarehouseNameEqualTo())
        );

        return warehouseStorageCenters.stream().map(WarehouseStorageCenterConverter::warehouseStorage2Entity).collect(Collectors.toList());
    }

    @Override
    public PageInfo<WarehouseStorageEntity> queryProxyWarehouseStoragePage(WarehouseStorageQuery warehouseStorageQuery) {
        //先查询绑定的仓库信息
        List<WncWarehouseStorageTenant> wncWarehouseStorageTenants = wncWarehouseStorageTenantMapper.selectList(new LambdaQueryWrapper<WncWarehouseStorageTenant>()
                .eq(WncWarehouseStorageTenant::getTenantId, warehouseStorageQuery.getTenantId())
        );
        if(CollectionUtils.isEmpty(wncWarehouseStorageTenants)){
            return new PageInfo<>();
        }
        List<Long> warehouseNos = wncWarehouseStorageTenants.stream().map(WncWarehouseStorageTenant::getWarehouseNo).collect(Collectors.toList());
        warehouseStorageQuery.setWarehouseNos(JSON.parseArray(warehouseNos.toString(),Integer.class));

        List<WarehouseStorageEntity> warehouseStorageEntities = new ArrayList<>();
        PageInfo<WarehouseStorageEntity> resultPageInfo = new PageInfo();
        //查询代仓仓库信息
        if(StringUtils.isBlank(warehouseStorageQuery.getWarehouseName()) && CollectionUtils.isEmpty(warehouseStorageQuery.getProvinces()) && CollectionUtils.isEmpty(warehouseStorageQuery.getCitys()) &&  CollectionUtils.isEmpty(warehouseStorageQuery.getAreas())){
            PageHelper.startPage(warehouseStorageQuery.getPageIndex(), warehouseStorageQuery.getPageSize());
            List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                    .in(WarehouseStorageCenter::getWarehouseNo,warehouseStorageQuery.getWarehouseNos())
                    .last("ORDER BY CONVERT(warehouse_name USING gbk) ASC")
            );
            PageInfo<WarehouseStorageCenter> pageInfo = PageInfoHelper.createPageInfo(warehouseStorageCenters);

            if(CollectionUtils.isEmpty(warehouseStorageCenters)){
                return new PageInfo<>();
            }
            warehouseStorageQuery.setWarehouseNos(warehouseStorageCenters.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList()));
            //查询围栏信息
            warehouseStorageEntities = warehouseStorageCenterMapper.queryProxyWarehouseStoragePage(warehouseStorageQuery);

            BeanUtil.copyProperties(pageInfo, resultPageInfo);
            resultPageInfo.setList(warehouseStorageEntities);
        }else if(StringUtils.isNotBlank(warehouseStorageQuery.getWarehouseName())){
            PageHelper.startPage(warehouseStorageQuery.getPageIndex(), warehouseStorageQuery.getPageSize());
            List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                    .in(WarehouseStorageCenter::getWarehouseNo,warehouseStorageQuery.getWarehouseNos())
                    .like(WarehouseStorageCenter::getWarehouseName, warehouseStorageQuery.getWarehouseName())
                    .last("ORDER BY CONVERT(warehouse_name USING gbk) ASC")
            );
            if(CollectionUtils.isEmpty(warehouseStorageCenters)){
                return new PageInfo<>();
            }
            PageInfo<WarehouseStorageCenter> pageInfo = PageInfoHelper.createPageInfo(warehouseStorageCenters);

            warehouseStorageQuery.setWarehouseNos(warehouseStorageCenters.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList()));
            //查询围栏信息
            warehouseStorageEntities = warehouseStorageCenterMapper.queryProxyWarehouseStoragePage(warehouseStorageQuery);
            BeanUtil.copyProperties(pageInfo, resultPageInfo);
            resultPageInfo.setList(warehouseStorageEntities);

            warehouseStorageQuery.setWarehouseNos(warehouseStorageEntities.stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList()));
        }else if(!CollectionUtils.isEmpty(warehouseStorageQuery.getProvinces() )|| !CollectionUtils.isEmpty(warehouseStorageQuery.getCitys()) || !CollectionUtils.isEmpty(warehouseStorageQuery.getAreas())){
            //需要查询出省市区对应的仓库信息，然后在查询对应的仓库
            warehouseStorageEntities = warehouseStorageCenterMapper.queryProxyWarehouseStoragePage(warehouseStorageQuery);
            if(CollectionUtils.isEmpty(warehouseStorageEntities)){
                return new PageInfo<WarehouseStorageEntity>();
            }
            PageHelper.startPage(warehouseStorageQuery.getPageIndex(), warehouseStorageQuery.getPageSize());
            List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                    .in(WarehouseStorageCenter::getWarehouseNo, warehouseStorageEntities.stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList()))
                    .last("ORDER BY CONVERT(warehouse_name USING gbk) ASC")
            );
            PageInfo<WarehouseStorageCenter> pageInfo = PageInfoHelper.createPageInfo(warehouseStorageCenters);

            //查询围栏信息
            warehouseStorageQuery.setWarehouseNos(warehouseStorageCenters.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList()));
            warehouseStorageEntities = warehouseStorageCenterMapper.queryProxyWarehouseStoragePage(warehouseStorageQuery);

            BeanUtil.copyProperties(pageInfo, resultPageInfo);
            resultPageInfo.setList(warehouseStorageEntities);
        }
        return resultPageInfo;
    }

    @Override
    public WarehouseStorageEntity queryById(Long id) {
        return WarehouseStorageCenterConverter.warehouseStorage2Entity(warehouseStorageCenterMapper.selectById(id));
    }

    @Override
    public PageInfo<WarehouseStorageEntity> queryFencePageList(WarehouseStorageQuery warehouseStorageQuery) {
        List<WarehouseStorageEntity> warehouseStorageEntities = new ArrayList<>();
        PageInfo<WarehouseStorageEntity> resultPageInfo = new PageInfo();

        //没有查询条件
        if(StringUtils.isBlank(warehouseStorageQuery.getWarehouseName()) && CollectionUtils.isEmpty(warehouseStorageQuery.getProvinces()) && CollectionUtils.isEmpty(warehouseStorageQuery.getCitys()) &&  CollectionUtils.isEmpty(warehouseStorageQuery.getAreas())){
            PageHelper.startPage(warehouseStorageQuery.getPageIndex(), warehouseStorageQuery.getPageSize());
            List<WncWarehouseStorageFence> wncWarehouseStorageFences = wncWarehouseStorageFenceMapper.selectList(new LambdaQueryWrapper<WncWarehouseStorageFence>()
                    .eq(WncWarehouseStorageFence::getTenantId, warehouseStorageQuery.getTenantId())
                    .groupBy(WncWarehouseStorageFence::getWarehouseNo)
                    .orderByDesc(WncWarehouseStorageFence::getUpdateTime)
                    .select(WncWarehouseStorageFence::getWarehouseNo));

            if(CollectionUtils.isEmpty(wncWarehouseStorageFences)){
                return new PageInfo<WarehouseStorageEntity>();
            }
            PageInfo<WncWarehouseStorageFence> pageInfo = PageInfoHelper.createPageInfo(wncWarehouseStorageFences);

            warehouseStorageQuery.setWarehouseNos(wncWarehouseStorageFences.stream().map(WncWarehouseStorageFence::getWarehouseNo).collect(Collectors.toList()));
            //查询围栏信息
            warehouseStorageEntities = queryWarehouseStorageEntities(warehouseStorageQuery);

            BeanUtil.copyProperties(pageInfo, resultPageInfo);
            resultPageInfo.setList(warehouseStorageEntities);
        }else if(StringUtils.isNotBlank(warehouseStorageQuery.getWarehouseName())){
            PageHelper.startPage(warehouseStorageQuery.getPageIndex(), warehouseStorageQuery.getPageSize());
            List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                    .eq(WarehouseStorageCenter::getTenantId, warehouseStorageQuery.getTenantId())
                    .like(WarehouseStorageCenter::getWarehouseName, warehouseStorageQuery.getWarehouseName())
                    .orderByDesc(WarehouseStorageCenter::getUpdateTime)
            );
            if(CollectionUtils.isEmpty(warehouseStorageCenters)){
                return new PageInfo<WarehouseStorageEntity>();
            }
            PageInfo<WarehouseStorageCenter> pageInfo = PageInfoHelper.createPageInfo(warehouseStorageCenters);

            warehouseStorageQuery.setWarehouseNos(warehouseStorageCenters.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList()));
            //查询围栏信息
            warehouseStorageEntities = queryWarehouseStorageEntities(warehouseStorageQuery);
            warehouseStorageQuery.setWarehouseNos(warehouseStorageEntities.stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList()));

            BeanUtil.copyProperties(pageInfo, resultPageInfo);
            resultPageInfo.setList(warehouseStorageEntities);
        }else if(!CollectionUtils.isEmpty(warehouseStorageQuery.getProvinces() ) || !CollectionUtils.isEmpty(warehouseStorageQuery.getCitys())
                || !CollectionUtils.isEmpty(warehouseStorageQuery.getAreas())){
            //需要查询出省市区对应的仓库信息，然后在查询对应的仓库
            warehouseStorageEntities = queryWarehouseStorageEntities(warehouseStorageQuery);
            if(CollectionUtils.isEmpty(warehouseStorageEntities)){
                return new PageInfo<WarehouseStorageEntity>();
            }
            PageHelper.startPage(warehouseStorageQuery.getPageIndex(), warehouseStorageQuery.getPageSize());
            List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                    .eq(WarehouseStorageCenter::getTenantId, warehouseStorageQuery.getTenantId())
                    .in(WarehouseStorageCenter::getWarehouseNo, warehouseStorageEntities.stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList()))
                    .orderByDesc(WarehouseStorageCenter::getUpdateTime)
            );
            PageInfo<WarehouseStorageCenter> pageInfo = PageInfoHelper.createPageInfo(warehouseStorageCenters);

            //查询围栏信息
            warehouseStorageQuery.setWarehouseNos(warehouseStorageCenters.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList()));
            warehouseStorageEntities = queryWarehouseStorageEntities(warehouseStorageQuery);

            BeanUtil.copyProperties(pageInfo, resultPageInfo);
            resultPageInfo.setList(warehouseStorageEntities);
        }
        return resultPageInfo;
    }

    @Override
    public List<WarehouseStorageEntity> queryListByStoreNo(Integer storeNo) {
        if(storeNo == null){
            return Collections.emptyList();
        }
        List<WarehouseLogisticsMapping> warehouseLogisticsMappings = warehouseLogisticsMappingMapper.selectList(new LambdaQueryWrapper<WarehouseLogisticsMapping>()
                .eq(WarehouseLogisticsMapping::getStoreNo, storeNo));
        if (CollectionUtils.isEmpty(warehouseLogisticsMappings)){
            return Collections.emptyList();
        }
        List<Integer> warehouseNos = warehouseLogisticsMappings.stream().map(WarehouseLogisticsMapping::getWarehouseNo).collect(Collectors.toList());
        return this.queryList(WarehouseStorageQuery.builder().warehouseNos(warehouseNos).build());
    }

    private List<WarehouseStorageEntity> queryWarehouseStorageEntities(WarehouseStorageQuery warehouseStorageQuery) {
        List<String> queryNoAreaCityList = new ArrayList<>();
        //过滤没有区域的城市
        List<String> noAreaCityList = configRepository.queryNoAreaCity();
        List<String> citys = warehouseStorageQuery.getCitys();
        List<String> newHaveAreaCitys = new ArrayList<>();
        if(!CollectionUtils.isEmpty(citys)){
            citys.forEach(city ->{
                if(noAreaCityList.contains(city)){
                    queryNoAreaCityList.add(city);
                }else{
                    newHaveAreaCitys.add(city);
                }
            });
        }
        List<WarehouseStorageEntity> warehouseStorageEntities = new ArrayList<>();
        //相等说明只查询没有区域的城市
        if(queryNoAreaCityList.size() == 0){
            //只查询只有城市和区域的
            warehouseStorageQuery.setCitys(newHaveAreaCitys);
            List<WarehouseStorageEntity> warehouseStorageList = warehouseStorageCenterMapper.querySelfWarehouseStorage(warehouseStorageQuery);
            warehouseStorageEntities.addAll(warehouseStorageList);
        }else{
            //只查询只有城市的
            warehouseStorageQuery.setCitys(queryNoAreaCityList);
            warehouseStorageQuery.setAreas(null);
            List<WarehouseStorageEntity> warehouseStorageList = warehouseStorageCenterMapper.querySelfWarehouseStorage(warehouseStorageQuery);
            warehouseStorageEntities.addAll(warehouseStorageList);
        }
        return warehouseStorageEntities;
    }

    @Override
    public PageInfo<WarehouseStorageEntity> queryWarehouseStoragePage(WarehouseStorageQuery warehouseStorageQuery) {
        PageInfo<WarehouseStorageEntity> resultPageInfo = new PageInfo();

        PageHelper.startPage(warehouseStorageQuery.getPageIndex(), warehouseStorageQuery.getPageSize());
        List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                .like(StringUtils.isNotBlank(warehouseStorageQuery.getWarehouseName()), WarehouseStorageCenter::getWarehouseName, warehouseStorageQuery.getWarehouseName())
                .eq(warehouseStorageQuery.getTenantId() != null,WarehouseStorageCenter::getTenantId, warehouseStorageQuery.getTenantId())
                .eq(warehouseStorageQuery.getStatus() != null, WarehouseStorageCenter::getStatus, warehouseStorageQuery.getStatus())
                .eq(warehouseStorageQuery.getWarehouseNo() != null, WarehouseStorageCenter::getWarehouseNo, warehouseStorageQuery.getWarehouseNo())
                .in(!CollectionUtils.isEmpty(warehouseStorageQuery.getWarehouseNos()), WarehouseStorageCenter::getWarehouseNo, warehouseStorageQuery.getWarehouseNos())
                .notIn(!CollectionUtils.isEmpty(warehouseStorageQuery.getNoInSelfWarehouseNos()), WarehouseStorageCenter::getWarehouseNo, warehouseStorageQuery.getNoInSelfWarehouseNos())
                .eq(warehouseStorageQuery.getType() != null, WarehouseStorageCenter::getType, warehouseStorageQuery.getType())
                .orderByDesc(WarehouseStorageCenter::getStatus)
                .orderByAsc(WarehouseStorageCenter::getId)
        );
        if(CollectionUtils.isEmpty(warehouseStorageCenters)){
            return resultPageInfo;
        }
        PageInfo<WarehouseStorageCenter> pageInfo = PageInfoHelper.createPageInfo(warehouseStorageCenters);
        BeanUtil.copyProperties(pageInfo, resultPageInfo);

        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenters.stream().map(WarehouseStorageCenterConverter::warehouseStorage2Entity).collect(Collectors.toList());
        resultPageInfo.setList(warehouseStorageEntities);
        return resultPageInfo;
    }

    @Override
    public long queryXmCountByNameAndId(String warehouseName, Integer id) {
        return warehouseStorageCenterMapper.selectCount(new LambdaQueryWrapper<WarehouseStorageCenter>()
                .eq(WarehouseStorageCenter::getTenantId, WarehouseStorageCenterEnums.TENANT.SUMMER_FARM.getValue())
                .eq(WarehouseStorageCenter::getWarehouseName, warehouseName)
                .ne(id != null,WarehouseStorageCenter::getId, id)
        );
    }

    @Override
    public void saveXmWarehouse(WarehouseStorageEntity warehouseStorageEntity) {
        //查询数量作为库存仓编号
        WarehouseStorageCenter warehouseStorageCenter = warehouseStorageCenterMapper.selectOne(new LambdaQueryWrapper<WarehouseStorageCenter>()
                .orderByDesc(WarehouseStorageCenter::getWarehouseNo).last("limit 1")
        );
        warehouseStorageEntity.setWarehouseNo(warehouseStorageCenter.getWarehouseNo() + 1);
        WarehouseStorageCenter warehouseStorageCenterSave = WarehouseStorageCenterConverter.entity2WarehouseStorage(warehouseStorageEntity);
        warehouseStorageCenterSave.setCreateTime(LocalDateTime.now());
        warehouseStorageCenterSave.setCreator(UserInfoHolder.getUser().getBizUserId());
        warehouseStorageCenterMapper.insert(warehouseStorageCenterSave);
        warehouseStorageEntity.setId(warehouseStorageCenterSave.getId());
        if(warehouseStorageEntity.getWarehouseStorageCenterBusEntity() != null){
            WarehouseStorageCenterBusiness warehouseStorageCenterBusiness = WarehouseStorageCenterBusinessConverter.entity2Model(warehouseStorageEntity.getWarehouseStorageCenterBusEntity());
            warehouseStorageCenterBusiness.setCapacity(warehouseStorageCenterBusiness.getCapacity() * 1000);
            warehouseStorageCenterBusiness.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
            warehouseStorageCenterBusinessMapper.insert(warehouseStorageCenterBusiness);

            if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseStorageCenterWorkEntities())){
                List<WarehouseStorageCenterBusinessWork> works = warehouseStorageEntity.getWarehouseStorageCenterWorkEntities().stream()
                        .map(WarehouseStorageCenterBusinessWorkConverter::entity2Model).collect(Collectors.toList());
                for (WarehouseStorageCenterBusinessWork work : works) {
                    work.setWarehouseStorageCenterBusinessId(warehouseStorageCenterBusiness.getId());
                    work.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
                    warehouseStorageCenterBusinessWorkMapper.insert(work);
                }
            }
        }
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseTakeStandardEntities())){
            List<WarehouseTakeStandard> warehouseTakeStandards = warehouseStorageEntity.getWarehouseTakeStandardEntities().stream()
                    .map(WarehouseTakeStandardConverter::entity2model).collect(Collectors.toList());
            for (WarehouseTakeStandard warehouseTakeStandard : warehouseTakeStandards) {
                warehouseTakeStandard.setWarehouseNo(warehouseStorageEntity.getWarehouseNo());
                warehouseTakeStandard.setCreateAdminId(UserInfoHolder.getUser().getBizUserId());
                warehouseTakeStandardMapper.insert(warehouseTakeStandard);
            }
        }
    }

    @Override
    public List<WarehouseStorageEntity> queryListWithBusinessWorkStandard(WarehouseStorageQuery warehouseStorageQuery) {
        List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                .eq(warehouseStorageQuery.getTenantId() != null,WarehouseStorageCenter::getTenantId,warehouseStorageQuery.getTenantId())
                .in(!CollectionUtils.isEmpty(warehouseStorageQuery.getWarehouseNos()),WarehouseStorageCenter::getWarehouseNo,warehouseStorageQuery.getWarehouseNos())
                .eq(warehouseStorageQuery.getType() != null,WarehouseStorageCenter::getType,warehouseStorageQuery.getType())
                .eq(warehouseStorageQuery.getStatus() != null,WarehouseStorageCenter::getStatus,warehouseStorageQuery.getStatus())
                .likeRight(StringUtils.isNotBlank(warehouseStorageQuery.getWarehouseName()),WarehouseStorageCenter::getWarehouseName,warehouseStorageQuery.getWarehouseName())
        );

        if(CollectionUtils.isEmpty(warehouseStorageCenters)){
            return Collections.emptyList();
        }
        List<Integer> warehouseNos = warehouseStorageCenters.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList());

        List<WarehouseStorageCenterBusiness> warehouseBusinessList = warehouseStorageCenterBusinessMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenterBusiness>()
                .in(WarehouseStorageCenterBusiness::getWarehouseNo, warehouseNos)
        );

        List<WarehouseTakeStandard> warehouseTakeStandards = warehouseTakeStandardMapper.selectList(new LambdaQueryWrapper<WarehouseTakeStandard>()
                .in(WarehouseTakeStandard::getWarehouseNo, warehouseNos)
        );

        List<WarehouseStorageCenterBusinessWork> warehouseWorks = warehouseStorageCenterBusinessWorkMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenterBusinessWork>()
                .in(WarehouseStorageCenterBusinessWork::getWarehouseNo, warehouseNos)
                .eq(WarehouseStorageCenterBusinessWork::getIsDelete, 0));

        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenters.stream().map(WarehouseStorageCenterConverter::warehouseStorage2Entity).collect(Collectors.toList());
        Map<Integer, List<WarehouseStorageCenterBusiness>> warehouseBusMap = warehouseBusinessList.stream().collect(Collectors.groupingBy(WarehouseStorageCenterBusiness::getWarehouseNo));
        Map<Integer, List<WarehouseTakeStandard>> warehouseStandardMap = warehouseTakeStandards.stream().collect(Collectors.groupingBy(WarehouseTakeStandard::getWarehouseNo));
        Map<Integer, List<WarehouseStorageCenterBusinessWork>> warehouseWorkMap = warehouseWorks.stream().collect(Collectors.groupingBy(WarehouseStorageCenterBusinessWork::getWarehouseNo));

        for (WarehouseStorageEntity warehouseStorageEntity : warehouseStorageEntities) {
            List<WarehouseStorageCenterBusiness> businesses = warehouseBusMap.get(warehouseStorageEntity.getWarehouseNo());
            List<WarehouseStorageCenterBusinessWork> works = warehouseWorkMap.get(warehouseStorageEntity.getWarehouseNo());
            List<WarehouseTakeStandard> standardList = warehouseStandardMap.get(warehouseStorageEntity.getWarehouseNo());

            if(!CollectionUtils.isEmpty(businesses)){
                warehouseStorageEntity.setWarehouseStorageCenterBusEntity(WarehouseStorageCenterBusinessConverter.warehouseStorageCenterBus2Entity(businesses.get(0)));
            }
            if(!CollectionUtils.isEmpty(works)){
                warehouseStorageEntity.setWarehouseStorageCenterWorkEntities(works.stream().map(WarehouseStorageCenterBusinessWorkConverter::model2Entity).collect(Collectors.toList()));
            }
            if(!CollectionUtils.isEmpty(standardList)){
                warehouseStorageEntity.setWarehouseTakeStandardEntities(standardList.stream().map(WarehouseTakeStandardConverter::model2Entity).collect(Collectors.toList()));
            }
        }

        return warehouseStorageEntities;
    }

    @Override
    public List<WarehouseStorageEntity> queryListByWarehouseNos(List<Integer> warehouseNos) {
        if(CollectionUtils.isEmpty(warehouseNos)){
            return Collections.emptyList();
        }
        List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                .in(WarehouseStorageCenter::getWarehouseNo, warehouseNos)
        );

        return warehouseStorageCenters.stream().map(WarehouseStorageCenterConverter::warehouseStorage2Entity).collect(Collectors.toList());
    }

    @Override
    public List<WarehouseStorageEntity> queryListByWarehouseNames(List<String> warehouseNames) {
        if(CollectionUtils.isEmpty(warehouseNames)){
            return Collections.emptyList();
        }
        List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenter>()
                .in(WarehouseStorageCenter::getWarehouseName, warehouseNames)
        );

        return warehouseStorageCenters.stream().map(WarehouseStorageCenterConverter::warehouseStorage2Entity).collect(Collectors.toList());
    }

    @Override
    public void updateStorage(WarehouseStorageEntity warehouseStorageEntity) {
        WarehouseStorageCenter warehouseStorageCenter = warehouseStorageCenterMapper.selectById(warehouseStorageEntity.getId());
        warehouseStorageCenterMapper.updateById(WarehouseStorageCenterConverter.entity2WarehouseStorage(warehouseStorageEntity));

        if(warehouseStorageEntity.getWarehouseStorageCenterBusEntity() != null){
            //删除老的
            warehouseStorageCenterBusinessMapper.delete(new LambdaQueryWrapper<WarehouseStorageCenterBusiness>()
                    .eq(WarehouseStorageCenterBusiness::getWarehouseNo, warehouseStorageCenter.getWarehouseNo()));
            WarehouseStorageCenterBusiness warehouseStorageCenterBusiness = WarehouseStorageCenterBusinessConverter.entity2Model(warehouseStorageEntity.getWarehouseStorageCenterBusEntity());
            warehouseStorageCenterBusiness.setWarehouseNo(warehouseStorageCenter.getWarehouseNo());
            //新增新的
            warehouseStorageCenterBusinessMapper.insert(warehouseStorageCenterBusiness);
        }
        //删除新增
        warehouseTakeStandardMapper.delete(new LambdaQueryWrapper<WarehouseTakeStandard>()
                .eq(WarehouseTakeStandard::getWarehouseNo,warehouseStorageCenter.getWarehouseNo()));
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseTakeStandardEntities())){
            List<WarehouseTakeStandard> warehouseTakeStandards = warehouseStorageEntity.getWarehouseTakeStandardEntities().stream().map(WarehouseTakeStandardConverter::entity2model).collect(Collectors.toList());
            for (WarehouseTakeStandard warehouseTakeStandard : warehouseTakeStandards) {
                warehouseTakeStandard.setWarehouseNo(warehouseStorageCenter.getWarehouseNo());
                warehouseTakeStandard.setCreateAdminId(warehouseStorageEntity.getCreator());
                warehouseTakeStandardMapper.insert(warehouseTakeStandard);
            }
        }
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseStorageFenceEntities())){
            //删除
            wncWarehouseStorageFenceMapper.delete(new LambdaQueryWrapper<WncWarehouseStorageFence>()
                    .eq(WncWarehouseStorageFence::getWarehouseNo,warehouseStorageCenter.getWarehouseNo()));
            List<WncWarehouseStorageFence> rcWarehouseStorageFences = warehouseStorageEntity.getWarehouseStorageFenceEntities().stream()
                    .map(WncWarehouseStorageFenceConverter::entity2RcWarehouseStorageFence).collect(Collectors.toList());

            for (WncWarehouseStorageFence rcWarehouseStorageFence : rcWarehouseStorageFences) {
                rcWarehouseStorageFence.setWarehouseNo(warehouseStorageCenter.getWarehouseNo());
                rcWarehouseStorageFence.setTenantId(warehouseStorageCenter.getTenantId());
                wncWarehouseStorageFenceMapper.insert(rcWarehouseStorageFence);
            }
        }
        if(!CollectionUtils.isEmpty(warehouseStorageEntity.getWarehouseStorageCenterWorkEntities())){
            List<WarehouseStorageCenterBusiness> businesses = warehouseStorageCenterBusinessMapper.selectList(new LambdaQueryWrapper<WarehouseStorageCenterBusiness>()
                    .eq(WarehouseStorageCenterBusiness::getWarehouseNo, warehouseStorageCenter.getWarehouseNo())
            );
            if(!CollectionUtils.isEmpty(businesses)){
                //删除新增
                warehouseStorageCenterBusinessWorkMapper.delete(new LambdaQueryWrapper<WarehouseStorageCenterBusinessWork>()
                        .eq(WarehouseStorageCenterBusinessWork::getWarehouseNo,warehouseStorageCenter.getWarehouseNo()));

                List<WarehouseStorageCenterBusinessWork> works = warehouseStorageEntity.getWarehouseStorageCenterWorkEntities().stream()
                        .map(WarehouseStorageCenterBusinessWorkConverter::entity2Model).collect(Collectors.toList());
                for (WarehouseStorageCenterBusinessWork work : works) {
                    work.setWarehouseNo(warehouseStorageCenter.getWarehouseNo());
                    work.setWarehouseStorageCenterBusinessId(businesses.get(0).getId());
                    warehouseStorageCenterBusinessWorkMapper.insert(work);
                }
            }
        }
    }

    @Override
    public Map<Integer, String> queryWarehouseNoToNameByWarehouseNos(Set<Integer> allWarehouseNoList) {
        if (CollectionUtils.isEmpty(allWarehouseNoList)) {
            return Collections.emptyMap();
        }
        List<WarehouseStorageEntity> warehouseStorageEntities = this.queryListByWarehouseNos(new ArrayList<>(allWarehouseNoList));
        if (!CollectionUtils.isEmpty(warehouseStorageEntities)) {
            return warehouseStorageEntities.stream().collect(Collectors.toMap(WarehouseStorageEntity::getWarehouseNo, WarehouseStorageEntity::getWarehouseName, (v1, v2) -> v1));
        }

        return Collections.emptyMap();
    }
}
