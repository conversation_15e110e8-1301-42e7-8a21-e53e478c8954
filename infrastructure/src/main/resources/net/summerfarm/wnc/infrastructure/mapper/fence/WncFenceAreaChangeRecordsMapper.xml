<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.fence.WncFenceAreaChangeRecordsMapper">
    <!-- 结果集映射 -->
    <resultMap id="wncFenceAreaChangeRecordsResultMap" type="net.summerfarm.wnc.infrastructure.model.fence.WncFenceAreaChangeRecords">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="fence_change_id" property="fenceChangeId" jdbcType="NUMERIC"/>
		<result column="fence_id" property="fenceId" jdbcType="NUMERIC"/>
		<result column="city" property="city" jdbcType="VARCHAR"/>
		<result column="area" property="area" jdbcType="VARCHAR"/>
		<result column="ad_code_msg_id" property="adCodeMsgId" jdbcType="NUMERIC"/>
		<result column="custom_area_name" property="customAreaName" jdbcType="VARCHAR"/>
		<result column="ad_code_msg_detail" property="adCodeMsgDetail" jdbcType="VARCHAR"/>
		<result column="geo_shape" property="geoShape" jdbcType="LONGVARCHAR"/>
		<result column="fence_change_stage" property="fenceChangeStage" jdbcType="INTEGER"/>
        <result column="area_draw_type" property="areaDrawType" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wncFenceAreaChangeRecordsColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.fence_change_id,
          t.fence_id,
          t.city,
          t.area,
          t.ad_code_msg_id,
          t.custom_area_name,
          t.ad_code_msg_detail,
          t.geo_shape,
          t.fence_change_stage,
          t.area_draw_type
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="fenceChangeId != null">
                AND t.fence_change_id = #{fenceChangeId}
            </if>
            <if test="fenceChangeIds != null and fenceChangeIds.size() > 0">
                AND t.fence_change_id IN
                <foreach collection="fenceChangeIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
			<if test="fenceId != null">
                AND t.fence_id = #{fenceId}
            </if>
			<if test="city != null and city !=''">
                AND t.city = #{city}
            </if>
			<if test="area != null and area !=''">
                AND t.area = #{area}
            </if>
			<if test="adCodeMsgId != null">
                AND t.ad_code_msg_id = #{adCodeMsgId}
            </if>
			<if test="customAreaName != null and customAreaName !=''">
                AND t.custom_area_name = #{customAreaName}
            </if>
			<if test="adCodeMsgDetail != null and adCodeMsgDetail !=''">
                AND t.ad_code_msg_detail = #{adCodeMsgDetail}
            </if>
			<if test="geoShape != null and geoShape !=''">
                AND t.geo_shape = #{geoShape}
            </if>
			<if test="fenceChangeStage != null">
                AND t.fence_change_stage = #{fenceChangeStage}
            </if>
            <if test="areaDrawType != null">
                AND t.area_draw_type = #{areaDrawType}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="fenceChangeId != null">
                    t.fence_change_id = #{fenceChangeId},
                </if>
                <if test="fenceId != null">
                    t.fence_id = #{fenceId},
                </if>
                <if test="city != null">
                    t.city = #{city},
                </if>
                <if test="area != null">
                    t.area = #{area},
                </if>
                <if test="adCodeMsgId != null">
                    t.ad_code_msg_id = #{adCodeMsgId},
                </if>
                <if test="customAreaName != null">
                    t.custom_area_name = #{customAreaName},
                </if>
                <if test="adCodeMsgDetail != null">
                    t.ad_code_msg_detail = #{adCodeMsgDetail},
                </if>
                <if test="geoShape != null">
                    t.geo_shape = #{geoShape},
                </if>
                <if test="fenceChangeStage != null">
                    t.fence_change_stage = #{fenceChangeStage},
                </if>
                <if test="areaDrawType != null">
                    t.area_draw_type = #{areaDrawType},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wncFenceAreaChangeRecordsResultMap" >
        SELECT <include refid="wncFenceAreaChangeRecordsColumns" />
        FROM wnc_fence_area_change_records t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wnc.domain.fence.param.query.WncFenceAreaChangeRecordsQueryParam"  resultType="net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.fence_change_id fenceChangeId,
            t.fence_id fenceId,
            t.city city,
            t.area area,
            t.ad_code_msg_id adCodeMsgId,
            t.custom_area_name customAreaName,
            t.ad_code_msg_detail adCodeMsgDetail,
            t.geo_shape geoShape,
            t.fence_change_stage fenceChangeStage,
            t.area_draw_type areaDrawType
        FROM wnc_fence_area_change_records t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wnc.domain.fence.param.query.WncFenceAreaChangeRecordsQueryParam" resultMap="wncFenceAreaChangeRecordsResultMap" >
        SELECT <include refid="wncFenceAreaChangeRecordsColumns" />
        FROM wnc_fence_area_change_records t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncFenceAreaChangeRecords" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wnc_fence_area_change_records
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="fenceChangeId != null">
				  fence_change_id,
              </if>
              <if test="fenceId != null">
				  fence_id,
              </if>
              <if test="city != null">
				  city,
              </if>
              <if test="area != null">
				  area,
              </if>
              <if test="adCodeMsgId != null">
				  ad_code_msg_id,
              </if>
              <if test="customAreaName != null">
				  custom_area_name,
              </if>
              <if test="adCodeMsgDetail != null">
				  ad_code_msg_detail,
              </if>
              <if test="geoShape != null">
				  geo_shape,
              </if>
              <if test="fenceChangeStage != null">
				  fence_change_stage,
              </if>
              <if test="areaDrawType != null">
                  area_draw_type,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="fenceChangeId != null">
				#{fenceChangeId,jdbcType=NUMERIC},
              </if>
              <if test="fenceId != null">
				#{fenceId,jdbcType=NUMERIC},
              </if>
              <if test="city != null">
				#{city,jdbcType=VARCHAR},
              </if>
              <if test="area != null">
				#{area,jdbcType=VARCHAR},
              </if>
              <if test="adCodeMsgId != null">
				#{adCodeMsgId,jdbcType=NUMERIC},
              </if>
              <if test="customAreaName != null">
				#{customAreaName,jdbcType=VARCHAR},
              </if>
              <if test="adCodeMsgDetail != null">
				#{adCodeMsgDetail,jdbcType=VARCHAR},
              </if>
              <if test="geoShape != null">
				#{geoShape,jdbcType=LONGVARCHAR},
              </if>
              <if test="fenceChangeStage != null">
				#{fenceChangeStage,jdbcType=INTEGER},
              </if>
              <if test="areaDrawType != null">
                #{areaDrawType,jdbcType=INTEGER},
             </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncFenceAreaChangeRecords" >
        UPDATE wnc_fence_area_change_records t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncFenceAreaChangeRecords" >
        DELETE FROM wnc_fence_area_change_records
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>

    <update id="updateRecordsFenceId">
        UPDATE wnc_fence_area_change_records
        SET fence_id = #{fenceId}
        WHERE id IN
        <foreach item="item" index="index" collection="areaIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <delete id="deleteByFenceChangeIds">
        DELETE FROM wnc_fence_area_change_records
        WHERE fence_change_id IN
        <foreach collection="wncFenceChangeRecordsIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>


</mapper>