<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.fence.WncCityAreaChangeWarehouseRecordsMapper">
    <!-- 结果集映射 -->
    <resultMap id="wncCityAreaChangeWarehouseRecordsResultMap" type="net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="area" property="area" jdbcType="VARCHAR"/>
		<result column="city" property="city" jdbcType="VARCHAR"/>
		<result column="change_status" property="changeStatus" jdbcType="INTEGER"/>
		<result column="pre_exe_time" property="preExeTime" jdbcType="TIMESTAMP"/>
		<result column="change_batch_no" property="changeBatchNo" jdbcType="VARCHAR"/>
		<result column="fence_change_task_id" property="fenceChangeTaskId" jdbcType="NUMERIC"/>
		<result column="effective_time" property="effectiveTime" jdbcType="TIMESTAMP"/>
		<result column="over_time" property="overTime" jdbcType="TIMESTAMP"/>
		<result column="area_defination_type" property="areaDefinationType" jdbcType="INTEGER"/>
		<result column="province" property="province" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wncCityAreaChangeWarehouseRecordsColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.area,
          t.city,
          t.change_status,
          t.pre_exe_time,
          t.change_batch_no,
          t.fence_change_task_id,
          t.effective_time,
          t.over_time,
          t.area_defination_type,
          t.province
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="area != null and area !=''">
                AND t.area = #{area}
            </if>
            <if test="areaList != null and areaList.size() > 0">
                AND t.area IN
                <foreach collection="areaList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
			<if test="city != null and city !=''">
                AND t.city = #{city}
            </if>
            <if test="cityList != null and cityList.size() > 0">
                AND t.city IN
                <foreach collection="cityList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
			<if test="changeStatus != null">
                AND t.change_status = #{changeStatus}
            </if>
            <if test="changeStatusList != null and changeStatusList.size() > 0">
                AND t.change_status IN
                <foreach collection="changeStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
			<if test="preExeTime != null">
                AND t.pre_exe_time = #{preExeTime}
            </if>
			<if test="changeBatchNo != null and changeBatchNo !=''">
                AND t.change_batch_no = #{changeBatchNo}
            </if>
			<if test="fenceChangeTaskId != null">
                AND t.fence_change_task_id = #{fenceChangeTaskId}
            </if>
			<if test="effectiveTime != null">
                AND t.effective_time = #{effectiveTime}
            </if>
			<if test="overTime != null">
                AND t.over_time = #{overTime}
            </if>
			<if test="areaDefinationType != null">
                AND t.area_defination_type = #{areaDefinationType}
            </if>
			<if test="province != null and province !=''">
                AND t.province = #{province}
            </if>
            <if test="fenceChangeTaskIds != null and fenceChangeTaskIds.size() > 0">
                AND t.fence_change_task_id IN
                <foreach item="item" index="index" collection="fenceChangeTaskIds"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="fenceName != null and fenceName !=''">
                AND fcr.fence_name = #{fenceName}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="area != null">
                    t.area = #{area},
                </if>
                <if test="city != null">
                    t.city = #{city},
                </if>
                <if test="changeStatus != null">
                    t.change_status = #{changeStatus},
                </if>
                <if test="preExeTime != null">
                    t.pre_exe_time = #{preExeTime},
                </if>
                <if test="changeBatchNo != null">
                    t.change_batch_no = #{changeBatchNo},
                </if>
                <if test="fenceChangeTaskId != null">
                    t.fence_change_task_id = #{fenceChangeTaskId},
                </if>
                <if test="effectiveTime != null">
                    t.effective_time = #{effectiveTime},
                </if>
                <if test="overTime != null">
                    t.over_time = #{overTime},
                </if>
                <if test="areaDefinationType != null">
                    t.area_defination_type = #{areaDefinationType},
                </if>
                <if test="province != null">
                    t.province = #{province},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wncCityAreaChangeWarehouseRecordsResultMap" >
        SELECT <include refid="wncCityAreaChangeWarehouseRecordsColumns" />
        FROM wnc_city_area_change_warehouse_records t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam"  resultType="net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity" >
        SELECT DISTINCT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.area area,
            t.city city,
            t.change_status changeStatus,
            t.pre_exe_time preExeTime,
            t.change_batch_no changeBatchNo,
            t.fence_change_task_id fenceChangeTaskId,
            t.effective_time effectiveTime,
            t.over_time overTime,
            t.area_defination_type areaDefinationType,
            t.province province
        FROM wnc_city_area_change_warehouse_records t
        LEFT JOIN wnc_fence_change_records fcr ON t.change_batch_no = fcr.change_batch_no
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam" resultMap="wncCityAreaChangeWarehouseRecordsResultMap" >
        SELECT <include refid="wncCityAreaChangeWarehouseRecordsColumns" />
        FROM wnc_city_area_change_warehouse_records t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wnc_city_area_change_warehouse_records
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="area != null">
				  area,
              </if>
              <if test="city != null">
				  city,
              </if>
              <if test="changeStatus != null">
				  change_status,
              </if>
              <if test="preExeTime != null">
				  pre_exe_time,
              </if>
              <if test="changeBatchNo != null">
				  change_batch_no,
              </if>
              <if test="fenceChangeTaskId != null">
				  fence_change_task_id,
              </if>
              <if test="effectiveTime != null">
				  effective_time,
              </if>
              <if test="overTime != null">
				  over_time,
              </if>
              <if test="areaDefinationType != null">
				  area_defination_type,
              </if>
              <if test="province != null">
				  province,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="area != null">
				#{area,jdbcType=VARCHAR},
              </if>
              <if test="city != null">
				#{city,jdbcType=VARCHAR},
              </if>
              <if test="changeStatus != null">
				#{changeStatus,jdbcType=INTEGER},
              </if>
              <if test="preExeTime != null">
				#{preExeTime,jdbcType=TIMESTAMP},
              </if>
              <if test="changeBatchNo != null">
				#{changeBatchNo,jdbcType=VARCHAR},
              </if>
              <if test="fenceChangeTaskId != null">
				#{fenceChangeTaskId,jdbcType=NUMERIC},
              </if>
              <if test="effectiveTime != null">
				#{effectiveTime,jdbcType=TIMESTAMP},
              </if>
              <if test="overTime != null">
				#{overTime,jdbcType=TIMESTAMP},
              </if>
              <if test="areaDefinationType != null">
				#{areaDefinationType,jdbcType=INTEGER},
              </if>
              <if test="province != null">
				#{province,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO wnc_city_area_change_warehouse_records (
             area, city, change_status, pre_exe_time,
            change_batch_no, fence_change_task_id, effective_time, over_time,
            area_defination_type, province
        ) VALUES
        <foreach collection="list" item="record" separator=",">
            (
                #{record.area,jdbcType=VARCHAR},
                #{record.city,jdbcType=VARCHAR},
                #{record.changeStatus,jdbcType=INTEGER},
                #{record.preExeTime,jdbcType=TIMESTAMP},
                #{record.changeBatchNo,jdbcType=VARCHAR},
                #{record.fenceChangeTaskId,jdbcType=NUMERIC},
                #{record.effectiveTime,jdbcType=TIMESTAMP},
                #{record.overTime,jdbcType=TIMESTAMP},
                #{record.areaDefinationType,jdbcType=INTEGER},
                #{record.province,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords" >
        UPDATE wnc_city_area_change_warehouse_records t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords" >
        DELETE FROM wnc_city_area_change_warehouse_records
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>

    <!-- 批量更新 - 使用CASE WHEN优化性能 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <if test="records != null and records.size() > 0">
            UPDATE wnc_city_area_change_warehouse_records
            <trim prefix="SET" suffixOverrides=",">
                <!-- 更新时间 -->
                update_time = CASE id
                <foreach collection="records" item="record">
                    <if test="record.updateTime != null">
                        WHEN #{record.id} THEN #{record.updateTime}
                    </if>
                </foreach>
                ELSE update_time END,

                <!-- 区域 -->
                area = CASE id
                <foreach collection="records" item="record">
                    <if test="record.area != null">
                        WHEN #{record.id} THEN #{record.area}
                    </if>
                </foreach>
                ELSE area END,

                <!-- 城市 -->
                city = CASE id
                <foreach collection="records" item="record">
                    <if test="record.city != null">
                        WHEN #{record.id} THEN #{record.city}
                    </if>
                </foreach>
                ELSE city END,

                <!-- 变更状态 -->
                change_status = CASE id
                <foreach collection="records" item="record">
                    <if test="record.changeStatus != null">
                        WHEN #{record.id} THEN #{record.changeStatus}
                    </if>
                </foreach>
                ELSE change_status END,

                <!-- 预约执行时间 -->
                pre_exe_time = CASE id
                <foreach collection="records" item="record">
                    <if test="record.preExeTime != null">
                        WHEN #{record.id} THEN #{record.preExeTime}
                    </if>
                </foreach>
                ELSE pre_exe_time END,

                <!-- 变更批次号 -->
                change_batch_no = CASE id
                <foreach collection="records" item="record">
                    <if test="record.changeBatchNo != null">
                        WHEN #{record.id} THEN #{record.changeBatchNo}
                    </if>
                </foreach>
                ELSE change_batch_no END,

                <!-- 切仓任务ID -->
                fence_change_task_id = CASE id
                <foreach collection="records" item="record">
                    <!-- 支持设置为null来清空字段 -->
                    WHEN #{record.id} THEN #{record.fenceChangeTaskId}
                </foreach>
                ELSE fence_change_task_id END,

                <!-- 生效时间 -->
                effective_time = CASE id
                <foreach collection="records" item="record">
                    <if test="record.effectiveTime != null">
                        WHEN #{record.id} THEN #{record.effectiveTime}
                    </if>
                </foreach>
                ELSE effective_time END,

                <!-- 结束时间 -->
                over_time = CASE id
                <foreach collection="records" item="record">
                    <if test="record.overTime != null">
                        WHEN #{record.id} THEN #{record.overTime}
                    </if>
                </foreach>
                ELSE over_time END,

                <!-- 区域定义类型 -->
                area_defination_type = CASE id
                <foreach collection="records" item="record">
                    <if test="record.areaDefinationType != null">
                        WHEN #{record.id} THEN #{record.areaDefinationType}
                    </if>
                </foreach>
                ELSE area_defination_type END,

                <!-- 省份 -->
                province = CASE id
                <foreach collection="records" item="record">
                    <if test="record.province != null">
                        WHEN #{record.id} THEN #{record.province}
                    </if>
                </foreach>
                ELSE province END
            </trim>
            WHERE id IN
            <foreach collection="records" item="record" open="(" separator="," close=")">
                #{record.id}
            </foreach>
        </if>
    </update>

    <!-- 根据切仓任务ID更新城市区域切仓记录的状态 -->
    <update id="updateChangeStatusByFenceChangeTaskId">
        UPDATE wnc_city_area_change_warehouse_records
        SET change_status = #{changeStatus}
        WHERE fence_change_task_id = #{fenceChangeTaskId}
    </update>

    <update id="resetCustomAreaFenceChangeTask">
        UPDATE wnc_city_area_change_warehouse_records
        SET pre_exe_time = null, fence_change_task_id = null, change_status = #{changeStatus}
        WHERE fence_change_task_id = #{fenceChangeTaskId}
    </update>

    <select id="selectExecutableRecords" resultMap="wncCityAreaChangeWarehouseRecordsResultMap">
        SELECT <include refid="wncCityAreaChangeWarehouseRecordsColumns" />
        FROM wnc_city_area_change_warehouse_records t
        where t.pre_exe_time &lt;= #{preExeTime} and t.change_status = #{changeStatus} and t.area_defination_type = #{areaDefinationType}
    </select>

    <update id="updateChangeStatusByIds">
        UPDATE wnc_city_area_change_warehouse_records
        SET change_status = #{targetStatus}
        WHERE id IN
        <foreach collection="cityAreaChangeIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND change_status = #{sourceStatus}
    </update>

    <delete id="deleteByFenceChangeTaskId">
        DELETE FROM wnc_city_area_change_warehouse_records
        WHERE fence_change_task_id = #{fenceChangeTaskId}
    </delete>

    <select id="updateChangeStatusWithEffectiveTime">
        UPDATE wnc_city_area_change_warehouse_records
        SET change_status = #{targetStatus}, effective_time = #{effectiveTime}
        WHERE id IN
        <foreach collection="cityAreaChangeIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND change_status = #{sourceStatus}
    </select>

    <select id="updateChangeStatusWithOverTime">
        UPDATE wnc_city_area_change_warehouse_records
        SET change_status = #{targetStatus}, over_time = #{overTime}
        WHERE id IN
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND change_status = #{sourceStatus}
    </select>
</mapper>