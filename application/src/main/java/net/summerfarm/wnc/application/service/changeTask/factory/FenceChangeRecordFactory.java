package net.summerfarm.wnc.application.service.changeTask.factory;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeRecordsConverter;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.fence.AreaRepository;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsMappingRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 围栏变更记录工厂类
 * date: 2025/9/2 9:57<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class FenceChangeRecordFactory {
    @Resource
    private WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;
    @Resource
    private AreaRepository areaRepository;
    @Resource
    private WarehouseLogisticsMappingRepository warehouseLogisticsMappingRepository;
    @Resource
    private WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    @Resource
    private CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;


    public List<WncFenceChangeRecordsCommandParam> buildFenceAreaRecord(List<FenceEntity> fenceDetailEntityList, String changeBatchNo, Long fenceChangeTaskId, WncFenceChangeRecordsEnums.FenceChangeStage fenceChangeStage) {
        if(CollectionUtils.isEmpty(fenceDetailEntityList)){
            return null;
        }
        // 查询基础数据映射关系（城配仓编号名称、运营区域编号名称、库存仓编号名称、城配仓映射库存仓）
        List<Integer> storeNos = fenceDetailEntityList.stream().map(FenceEntity::getStoreNo).collect(Collectors.toList());
        List<Integer> areaNos = fenceDetailEntityList.stream().map(FenceEntity::getAreaNo).collect(Collectors.toList());

        BaseDataMappings baseDataMappings = this.buildBaseDataMappings(storeNos, areaNos);

        // 设置POI信息
        List<AdCodeMsgEntity> adCodeMsgEntities = fenceDetailEntityList.stream()
                .map(FenceEntity::getAdCodeMsgEntities)
                .flatMap(Collection::stream).collect(Collectors.toList());

        for (AdCodeMsgEntity adCodeMsgEntity : adCodeMsgEntities) {
            String poi = customFenceAreaEsQueryRepository.findPoiByAdCodeMsgIds(Collections.singletonList(adCodeMsgEntity.getId()));
            adCodeMsgEntity.setPoi(poi);
        }

        // 使用转换器将FenceEntity转换为历史记录参数
        return FenceChangeRecordsConverter.convertToFenceChangeRecords(fenceDetailEntityList,
                changeBatchNo,fenceChangeStage.getValue(),baseDataMappings.getStoreNoToNameMap(), baseDataMappings.getAreaNoToNameMap(),
                baseDataMappings.getStoreNoToWarehouseNoListMap(), baseDataMappings.getWarehouseNoToNameMap(),
                fenceChangeTaskId);
    }


    /**
     * 构建基础数据映射关系
     */
    public BaseDataMappings buildBaseDataMappings(List<Integer> storeNoList, List<Integer> areaNos) {
        if(CollectionUtils.isEmpty(storeNoList) || CollectionUtils.isEmpty(areaNos)){
            return null;
        }

        // 批量查询基础数据
        Map<Integer, String> storeNoToNameMap = warehouseLogisticsCenterRepository.queryStoreNoToNameMapByStoreNos(storeNoList);
        Map<Integer, String> areaNoToNameMap = areaRepository.queryAreaNoToNameMapByAreaNos(areaNos);
        Map<Integer, List<Integer>> storeNoToWarehouseNoListMap = warehouseLogisticsMappingRepository.queryLogisticsMapping(storeNoList);

        // 获取所有库存仓编号
        Set<Integer> allWarehouseNoList = new HashSet<>();
        storeNoToWarehouseNoListMap.forEach((storeNo, warehouseNoList) -> {
            if (!CollectionUtils.isEmpty(warehouseNoList)) {
                allWarehouseNoList.addAll(warehouseNoList);
            }
        });

        Map<Integer, String> warehouseNoToNameMap = warehouseStorageCenterRepository.queryWarehouseNoToNameByWarehouseNos(allWarehouseNoList);

        return new BaseDataMappings(storeNoToNameMap, areaNoToNameMap, storeNoToWarehouseNoListMap, warehouseNoToNameMap);
    }

    /**
     * 基础数据映射关系封装类
     */
    @Data
    public static class BaseDataMappings {
        private final Map<Integer, String> storeNoToNameMap;
        private final Map<Integer, String> areaNoToNameMap;
        private final Map<Integer, List<Integer>> storeNoToWarehouseNoListMap;
        private final Map<Integer, String> warehouseNoToNameMap;

        public BaseDataMappings(Map<Integer, String> storeNoToNameMap,
                                Map<Integer, String> areaNoToNameMap,
                                Map<Integer, List<Integer>> storeNoToWarehouseNoListMap,
                                Map<Integer, String> warehouseNoToNameMap) {
            this.storeNoToNameMap = storeNoToNameMap != null ? storeNoToNameMap : new HashMap<>();
            this.areaNoToNameMap = areaNoToNameMap != null ? areaNoToNameMap : new HashMap<>();
            this.storeNoToWarehouseNoListMap = storeNoToWarehouseNoListMap != null ? storeNoToWarehouseNoListMap : new HashMap<>();
            this.warehouseNoToNameMap = warehouseNoToNameMap != null ? warehouseNoToNameMap : new HashMap<>();
        }
    }
}
