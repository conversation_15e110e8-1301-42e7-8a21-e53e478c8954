package net.summerfarm.wnc.application.service.changeTask.converter;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 围栏历史记录转换器
 * 用于将FenceEntity转换为历史记录相关的Command参数
 */
@Slf4j
public class FenceChangeRecordsConverter {

    /**
     * 将FenceEntity列表转换为WncFenceChangeRecordsCommandParam列表
     *
     * @param fenceEntityList             围栏实体列表
     * @param changeBatchNo               变更批次号
     * @param fenceChangeStage            围栏变更阶段 0变更前 1变更后 2结束时
     * @param storeNoToNameMap            城配仓编号对应名称
     * @param areaNoToNameMap             运营区域编号对应名称
     * @param storeNoToWarehouseNoListMap 城配仓编号对应库存仓编号列表
     * @param warehouseNoToNameMap        库存仓编号对应名称
     * @param fenceChangeTaskId           切仓任务ID
     * @return WncFenceChangeRecordsCommandParam列表
     */
    public static List<WncFenceChangeRecordsCommandParam> convertToFenceChangeRecords(
            List<FenceEntity> fenceEntityList, String changeBatchNo,
            Integer fenceChangeStage, Map<Integer, String> storeNoToNameMap, Map<Integer, String> areaNoToNameMap,
            Map<Integer, List<Integer>> storeNoToWarehouseNoListMap, Map<Integer, String> warehouseNoToNameMap, Long fenceChangeTaskId) {
        
        List<WncFenceChangeRecordsCommandParam> result = new ArrayList<>();
        
        if (CollectionUtils.isEmpty(fenceEntityList)) {
            return result;
        }
        
        for (FenceEntity fenceEntity : fenceEntityList) {
            if (fenceEntity == null || fenceEntity.getId() == null) {
                continue;
            }
            
            WncFenceChangeRecordsCommandParam param = new WncFenceChangeRecordsCommandParam();
            
            // 基础信息
            param.setFenceId(fenceEntity.getId());
            param.setFenceName(fenceEntity.getFenceName());
            param.setChangeBatchNo(changeBatchNo);
            param.setFenceChangeStage(fenceChangeStage);
            
            // 城配和区域信息
            param.setFenceStoreNo(fenceEntity.getStoreNo());
            param.setFenceAreaNo(fenceEntity.getAreaNo());
            param.setFenceStoreName(storeNoToNameMap.get(fenceEntity.getStoreNo()));
            param.setFenceAreaName(areaNoToNameMap.get(fenceEntity.getAreaNo()));

            // 切仓任务信息
            param.setFenceChangeTaskId(fenceChangeTaskId);
            
            // 库存仓信息
            List<Integer> warehouseNos = storeNoToWarehouseNoListMap.get(fenceEntity.getStoreNo());
            if (!CollectionUtils.isEmpty(warehouseNos)) {
                param.setFenceWarehouseNos(warehouseNos.stream().map(String::valueOf).collect(Collectors.joining(",")));

                StringJoiner warehouseNames = new StringJoiner(",");
                warehouseNos.forEach(warehouseNo -> {
                    if(warehouseNoToNameMap.get(warehouseNo) != null){
                        warehouseNames.add(warehouseNoToNameMap.get(warehouseNo));
                    }
                });
                param.setFenceWarehouseNames(warehouseNames.toString());
            }

            // 围栏详情信息 - 将整个FenceEntity序列化为JSON存储
            param.setFenceMsg(JSON.toJSONString(fenceEntity));

            // 区域变更记录
            param.setFenceAreaChangeRecords(convertToFenceAreaChangeRecords(fenceEntity, fenceChangeStage));

            result.add(param);
        }
        
        return result;
    }

    /**
     * 将FenceEntity列表转换为WncFenceAreaChangeRecordsCommandParam列表
     * 
     * @param fenceEntity 围栏实体
     * @param fenceChangeStage 围栏变更阶段 0变更前 1变更后 2结束时
     * @return WncFenceAreaChangeRecordsCommandParam列表
     */
    public static List<WncFenceAreaChangeRecordsCommandParam> convertToFenceAreaChangeRecords(FenceEntity fenceEntity, Integer fenceChangeStage) {
        if (fenceEntity == null) {
            return null;
        }
        List<WncFenceAreaChangeRecordsCommandParam> result = new ArrayList<>();

        // 处理围栏的区域编码信息
        List<AdCodeMsgEntity> adCodeMsgEntities = fenceEntity.getAdCodeMsgEntities();
        if (!CollectionUtils.isEmpty(adCodeMsgEntities)) {
            for (AdCodeMsgEntity adCodeMsgEntity : adCodeMsgEntities) {
                if (adCodeMsgEntity == null) {
                    continue;
                }

                WncFenceAreaChangeRecordsCommandParam param = new WncFenceAreaChangeRecordsCommandParam();

                // 基础信息
                param.setFenceId(fenceEntity.getId());
                param.setFenceChangeStage(fenceChangeStage);

                // 区域信息
                param.setCity(adCodeMsgEntity.getCity());
                param.setArea(adCodeMsgEntity.getArea());
                param.setAdCodeMsgId(adCodeMsgEntity.getId() != null ? adCodeMsgEntity.getId() : null);
                param.setCustomAreaName(adCodeMsgEntity.getCustomAreaName());

                // poi信息
                param.setGeoShape(adCodeMsgEntity.getPoi());

                // 区域详情信息 - 将AdCodeMsgEntity序列化为JSON存储
                param.setAdCodeMsgDetail(JSON.toJSONString(adCodeMsgEntity));

                result.add(param);
            }
        }

        return result;
    }

}
