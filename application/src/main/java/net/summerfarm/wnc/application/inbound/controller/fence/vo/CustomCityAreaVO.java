package net.summerfarm.wnc.application.inbound.controller.fence.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 自定义围栏
 * date: 2025/8/26 17:32<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomCityAreaVO {

    /**
     * 自定义城市区域变更记录ID
     */
    private Long cityAreaChangeWarehouseRecordId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 区域名称
     */
    private String area;

    /**
     * 自定义城市区域变更状态 0 等待生效 10正常生效中 20已结束
     */
    private Integer cityAreaChangeStatus;

    /**
     * 城配仓名称
     */
    private String storeNames;

    /**
     * 运营区域名称
     */
    private String areaNames;

    /**
     * 生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime overTime;

    /**
     * 预约切仓执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime preExeTime;

    /**
     * 围栏数量
     */
    private Integer fenceNum;

    /**
     * 围栏变更批次号
     */
    private String fenceChangeBatchNo;

    /**
     * 切仓任务ID
     */
    private Long fenceChangeTaskId;

    /**
     * 区域定义类型10 普通范围区域  20自定义范围区域
     */
    private Integer areaDefinationType;


}
