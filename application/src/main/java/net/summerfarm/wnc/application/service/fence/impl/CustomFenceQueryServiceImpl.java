package net.summerfarm.wnc.application.service.fence.impl;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CityAreaDetailInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaFenceDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaFenceAreaDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.converter.FenceVOConverter;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.*;
import net.summerfarm.wnc.application.service.fence.CustomFenceQueryService;
import net.summerfarm.wnc.application.inbound.controller.fence.converter.CustomFenceConverter;
import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.fence.FenceDomainService;
import net.summerfarm.wnc.domain.fence.entity.*;
import net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceAreaChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2025/8/26 17:30<br/>
 *
 * <AUTHOR> />
 */
@Service
public class CustomFenceQueryServiceImpl implements CustomFenceQueryService {

    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;

    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    @Resource
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;

    @Resource
    private FenceDomainService fenceDomainService;

    @Resource
    private CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;

    @Override
    public PageInfo<CustomCityAreaVO> queryPage(CustomCityAreaQueryInput input) {
        // 构建查询参数
        WncCityAreaChangeWarehouseRecordsQueryParam queryParam = new WncCityAreaChangeWarehouseRecordsQueryParam();
        if (!CollectionUtils.isEmpty(input.getCityAreaDetailInputList())) {
            queryParam.setAreaList(input.getCityAreaDetailInputList().stream().map(CityAreaDetailInput::getArea).collect(Collectors.toList()));
            queryParam.setCityList(input.getCityAreaDetailInputList().stream().map(CityAreaDetailInput::getCity).collect(Collectors.toList()));
        }
        queryParam.setChangeStatusList(input.getCityAreaChangeStatusList());
        queryParam.setFenceName(input.getFenceName());
        queryParam.setPageIndex(input.getPageIndex());
        queryParam.setPageSize(input.getPageSize());
        queryParam.setAreaDefinationType(WncCityAreaChangeWarehouseRecordsEnums.AreaDefinationType.CUSTOM.getValue());

        // 执行分页查询
        PageInfo<WncCityAreaChangeWarehouseRecordsEntity> entityPageInfo = wncCityAreaChangeWarehouseRecordsQueryRepository.getPage(queryParam);

        // 转换为DTO
        List<CustomCityAreaVO> dtoList = CustomFenceConverter.convertToCustomCityAreaVOList(entityPageInfo.getList());

        // 根据fenceChangeBatchNo查询围栏
        if (!CollectionUtils.isEmpty(dtoList)) {
            Set<String> fenceChangeBatchNos = dtoList.stream().map(CustomCityAreaVO::getFenceChangeBatchNo).collect(Collectors.toSet());
            List<WncFenceChangeRecordsEntity> wncFenceChangeRecordsEntityList = wncFenceChangeRecordsQueryRepository.selectByChangeBatchNoList(new ArrayList<>(fenceChangeBatchNos));
            Map<String, List<WncFenceChangeRecordsEntity>> fenceChangeRecordsMap = wncFenceChangeRecordsEntityList.stream().collect(Collectors.groupingBy(WncFenceChangeRecordsEntity::getChangeBatchNo));
            // 填充数据
            dtoList.forEach(dto -> {
                List<WncFenceChangeRecordsEntity> records = fenceChangeRecordsMap.get(dto.getFenceChangeBatchNo());
                if (!CollectionUtils.isEmpty(records)) {
                    // 生效中状态查询围栏
                    if (WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.EFFECTIVE.getValue().equals(dto.getCityAreaChangeStatus())) {
                        int count = (int) records.stream().filter(record -> WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue().equals(record.getFenceChangeStage())).count();
                        dto.setFenceNum(count);
                    } else if (WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.END.getValue().equals(dto.getCityAreaChangeStatus())) {
                        int count = (int) records.stream().filter(record -> WncFenceChangeRecordsEnums.FenceChangeStage.END.getValue().equals(record.getFenceChangeStage())).count();
                        dto.setFenceNum(count);
                    } else {
                        int count = (int) records.stream().filter(record -> WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue().equals(record.getFenceChangeStage())).count();
                        dto.setFenceNum(count);
                    }
                    dto.setStoreNames(records.stream().map(WncFenceChangeRecordsEntity::getFenceStoreName).distinct().collect(Collectors.joining(",")));
                    dto.setAreaNames(records.stream().map(WncFenceChangeRecordsEntity::getFenceAreaName).distinct().collect(Collectors.joining(",")));
                }
            });
        }


        // 构建返回的分页信息
        PageInfo<CustomCityAreaVO> result = new PageInfo<>(dtoList);
        result.setTotal(entityPageInfo.getTotal());
        result.setPageNum(entityPageInfo.getPageNum());
        result.setPageSize(entityPageInfo.getPageSize());
        result.setPages(entityPageInfo.getPages());

        return result;
    }

    @Override
    public CustomFenceDetailVO queryFenceAreaDetail(CustomCityAreaFenceDetailQueryInput input) {
        // 根据围栏变更记录ID查询主记录
        WncFenceChangeRecordsEntity fenceChangeRecord = wncFenceChangeRecordsQueryRepository.selectById(input.getFenceChangeRecordsId());
        if (fenceChangeRecord == null) {
            return null;
        }

        // 遍历围栏变更记录，查询对应的围栏区域变更记录
        WncFenceAreaChangeRecordsQueryParam areaChangeParam = new WncFenceAreaChangeRecordsQueryParam();
        areaChangeParam.setFenceChangeId(fenceChangeRecord.getId());
        List<WncFenceAreaChangeRecordsEntity> areaChangeRecords = wncFenceAreaChangeRecordsQueryRepository.selectByCondition(areaChangeParam);
        // 循环组装
        List<CustomAreaDetail> customAreaDetails = CustomFenceConverter.convertToCustomAreaDetailList(areaChangeRecords);

        return CustomFenceConverter.convertToCustomFenceDetailVO(fenceChangeRecord, customAreaDetails);
    }

    @Override
    public CustomCityAreaFenceAreaDetailVO queryCityAreaFenceAreaDetail(CustomCityAreaFenceAreaDetailQueryInput input) {
        // 根据城市区域变更记录ID查询主记录
        WncCityAreaChangeWarehouseRecordsEntity cityAreaRecord = wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(input.getCityAreaChangeWarehouseRecordId());
        if (cityAreaRecord == null) {
            return null;
        }
        CustomCityAreaFenceAreaDetailVO vo = new CustomCityAreaFenceAreaDetailVO();
        vo.setCityAreaChangeWarehouseRecordId(input.getCityAreaChangeWarehouseRecordId());
        vo.setProvince(cityAreaRecord.getProvince());
        vo.setCity(cityAreaRecord.getCity());
        vo.setArea(cityAreaRecord.getArea());
        vo.setCityAreaChangeStatus(cityAreaRecord.getChangeStatus());
        vo.setPreExeTime(cityAreaRecord.getPreExeTime());

        // 围栏列表
        List<CustomFenceDetailVO> customFenceDetails = new ArrayList<>();

        // 生效中状态查询围栏
        if (WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.EFFECTIVE.getValue().equals(cityAreaRecord.getChangeStatus())) {
            // 根据批次号查询围栏变更记录
            WncFenceChangeRecordsQueryParam fenceChangeParam = new WncFenceChangeRecordsQueryParam();
            fenceChangeParam.setChangeBatchNo(cityAreaRecord.getChangeBatchNo());
            fenceChangeParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
            List<WncFenceChangeRecordsEntity> fenceChangeRecords = wncFenceChangeRecordsQueryRepository.selectByCondition(fenceChangeParam);
            // 获取所有涉及的围栏ID
            List<Integer> fenceIds = fenceChangeRecords.stream()
                    .map(WncFenceChangeRecordsEntity::getFenceId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            List<FenceEntity> fenceEntities = fenceDomainService.queryFenceAllDetailList(fenceIds);

            if (!CollectionUtils.isEmpty(fenceEntities)) {
                for (FenceEntity fenceEntity : fenceEntities) {
                    List<AdCodeMsgEntity> adCodeMsgEntities = fenceEntity.getAdCodeMsgEntities();
                    if (CollectionUtils.isEmpty(adCodeMsgEntities)){
                        continue;
                    }
                    for (AdCodeMsgEntity adCodeMsgEntity : adCodeMsgEntities) {
                        String poi = customFenceAreaEsQueryRepository.findPoiByAdCodeMsgIds(Collections.singletonList(adCodeMsgEntity.getId()));
                        adCodeMsgEntity.setPoi(poi);
                    }
                }
                customFenceDetails = CustomFenceConverter.convertToCustomFenceDetailVOList(fenceEntities);
            }
        } else {
            // 其他状态查询围栏快照
            // 根据批次号查询围栏变更记录
            WncFenceChangeRecordsQueryParam fenceChangeParam = new WncFenceChangeRecordsQueryParam();
            fenceChangeParam.setChangeBatchNo(cityAreaRecord.getChangeBatchNo());
            fenceChangeParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.END.getValue());
            List<WncFenceChangeRecordsEntity> fenceChangeRecords = wncFenceChangeRecordsQueryRepository.selectByCondition(fenceChangeParam);
            if (CollectionUtils.isEmpty(fenceChangeRecords)){
                fenceChangeParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
                fenceChangeRecords = wncFenceChangeRecordsQueryRepository.selectByCondition(fenceChangeParam);
            }

            // 遍历围栏变更记录，查询对应的围栏区域变更记录
            List<Long> fenceChangeIdList = fenceChangeRecords.stream().map(WncFenceChangeRecordsEntity::getId).collect(Collectors.toList());
            WncFenceAreaChangeRecordsQueryParam areaChangeParam = new WncFenceAreaChangeRecordsQueryParam();
            areaChangeParam.setFenceChangeIds(fenceChangeIdList);
            List<WncFenceAreaChangeRecordsEntity> areaChangeRecords = wncFenceAreaChangeRecordsQueryRepository.selectByCondition(areaChangeParam);
            Map<Long, List<WncFenceAreaChangeRecordsEntity>> areaChangeRecordsMap = areaChangeRecords.stream()
                    .collect(Collectors.groupingBy(WncFenceAreaChangeRecordsEntity::getFenceChangeId));


            // 补充围栏区域
            for (WncFenceChangeRecordsEntity fenceChangeRecord : fenceChangeRecords) {
                List<WncFenceAreaChangeRecordsEntity> wncFenceAreaChangeRecordsEntityList = areaChangeRecordsMap.getOrDefault(fenceChangeRecord.getId(), new ArrayList<>());
                List<CustomAreaDetail> customAreaDetails = CustomFenceConverter.convertToCustomAreaDetailList(wncFenceAreaChangeRecordsEntityList);
                CustomFenceDetailVO fenceDetailVO = CustomFenceConverter.convertToCustomFenceDetailVO(fenceChangeRecord, customAreaDetails);
                if (null != fenceChangeRecord.getFenceDetailEntity()) {
                    FenceDeliveryVO fenceDeliveryVO = CustomFenceConverter.convertToFenceDeliveryVO(fenceChangeRecord.getFenceDetailEntity().getFenceDeliveryEntity());
                    fenceDetailVO.setFenceDeliveryVO(fenceDeliveryVO);
                    List<FenceChannelBusinessWhiteConfigVO> fenceChannelBusinessWhiteConfigCommandVOList = FenceVOConverter.entityList2VOList(fenceChangeRecord.getFenceDetailEntity().getFenceChannelBusinessWhiteConfigEntities());
                    fenceDetailVO.setFenceChannelBusinessWhiteConfigVOList(fenceChannelBusinessWhiteConfigCommandVOList);
                }
                customFenceDetails.add(fenceDetailVO);
            }
        }

        vo.setCustomFenceDetails(customFenceDetails);

        return vo;
    }



}
