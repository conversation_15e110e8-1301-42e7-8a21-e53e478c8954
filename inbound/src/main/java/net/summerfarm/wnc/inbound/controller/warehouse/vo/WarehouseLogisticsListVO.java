package net.summerfarm.wnc.inbound.controller.warehouse.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class WarehouseLogisticsListVO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 物流中心编号（配送仓编号）
     */
    private Integer storeNo;
    /**
     * 区域
     */
    private String region;
    /**
     * 名称
     */
    private String storeName;
    /**
     * 配送中心状态：0、失效 1、有效
     */
    private Integer status;
    /**
     * 是否支持提前截单：0、false 1、true
     */
    private Integer closeOrderType;

    /**
     * 库存使用仓
     */
    private String warehouseNames;

    /**
     * 截单时间
     */
    private String closeTime;

    /**
     * 停配规则状态 0未配置 1已设置 2已完成
     */
    private Integer stopDeliveryStatus;

    /**
     * 是否需要打卡0 不需要 1需要
     */
    private Integer punchState;

    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    private Integer fulfillmentType;

    /**
     * 履约类型描述
     */
    private String fulfillmentTypeDesc;
}
